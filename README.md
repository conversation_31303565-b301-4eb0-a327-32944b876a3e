# Flori Construction Ltd Website

A complete modern construction company website with both a public frontend and an administrative backend panel.

## Features

### Frontend Features
- **Homepage** with company overview and hero section
- **Services page** showcasing construction services offered
- **Projects gallery** with categories (recent projects, ongoing projects, completed projects)
- **Photo galleries** for project showcases
- **Contact information** and inquiry forms
- **About us/company** information page
- **Responsive design** for mobile and desktop

### Admin Panel Features
- **Secure login system** for administrators
- **Content Management System (CMS)** to update:
  - Website text content and descriptions
  - Services offered (add/edit/delete services)
  - Project portfolio (add/edit/delete projects with photos)
  - Photo galleries management
  - Company information updates
- **Real-time updates**: Changes made in admin panel immediately reflect on the public website
- **User-friendly interface** for non-technical users
- **Image upload and management system**

## Technical Requirements

- **PHP 7.4+**
- **MySQL 5.7+**
- **Apache/Nginx Web Server**
- **mod_rewrite enabled** (for Apache)

## Installation Instructions

### 1. Database Setup

1. Create a new MySQL database named `flori_construction`
2. Import the database schema:
   ```bash
   mysql -u your_username -p flori_construction < database.sql
   ```

### 2. Configuration

1. Update database credentials in `config/database.php`:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'flori_construction');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   ```

2. Update site URL in `config/config.php`:
   ```php
   define('SITE_URL', 'http://your-domain.com');
   ```

### 3. File Permissions

Set proper permissions for upload directories:
```bash
chmod 755 uploads/
chmod 755 uploads/services/
chmod 755 uploads/projects/
chmod 755 uploads/gallery/
```

### 4. Web Server Configuration

#### Apache (.htaccess)
Create `.htaccess` file in the root directory:
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)/?$ $1.php [L,QSA]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
```

#### Nginx
Add to your server block:
```nginx
location / {
    try_files $uri $uri/ $uri.php?$query_string;
}

location ~ \.php$ {
    fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include fastcgi_params;
}
```

### 5. Admin Access

Default admin credentials:
- **Username**: admin
- **Email**: <EMAIL>
- **Password**: admin123

**Important**: Change the default password immediately after first login!

## Directory Structure

```
flori-con/
├── admin/                  # Admin panel
│   ├── assets/            # Admin CSS/JS
│   ├── includes/          # Admin includes
│   ├── dashboard.php      # Admin dashboard
│   ├── login.php          # Admin login
│   └── ...
├── assets/                # Frontend assets
│   ├── css/              # Stylesheets
│   ├── js/               # JavaScript files
│   └── images/           # Static images
├── config/                # Configuration files
│   ├── config.php        # Main configuration
│   └── database.php      # Database configuration
├── includes/              # Frontend includes
│   ├── functions.php     # Common functions
│   ├── header.php        # Site header
│   └── footer.php        # Site footer
├── uploads/               # User uploaded files
│   ├── services/         # Service images
│   ├── projects/         # Project images
│   └── gallery/          # Gallery images
├── index.php             # Homepage
├── about.php             # About page
├── services.php          # Services page
├── contact.php           # Contact page
├── database.sql          # Database schema
└── README.md             # This file
```

## Usage Guide

### Adding Content

1. **Login to Admin Panel**
   - Navigate to `/admin/login.php`
   - Use admin credentials to login

2. **Managing Services**
   - Go to Services section in admin panel
   - Add/edit/delete services
   - Upload service images
   - Set service descriptions

3. **Managing Projects**
   - Go to Projects section in admin panel
   - Add new projects with details
   - Upload project images
   - Set project categories (completed/ongoing)

4. **Content Management**
   - Update homepage content
   - Modify about us information
   - Update company contact details

5. **Settings**
   - Configure company information
   - Update social media links
   - Manage website settings

### Image Requirements

- **Format**: JPG, PNG, GIF, WebP
- **Size**: Maximum 5MB per image
- **Recommended dimensions**:
  - Service images: 800x600px
  - Project images: 1200x800px
  - Hero background: 1920x1080px

## Security Features

- **SQL Injection Protection**: All database queries use prepared statements
- **XSS Protection**: All user inputs are sanitized
- **CSRF Protection**: Forms include CSRF tokens
- **Secure Authentication**: Passwords are hashed using PHP's password_hash()
- **Session Security**: Secure session management
- **File Upload Security**: Strict file type and size validation

## Customization

### Styling
- Main styles: `assets/css/style.css`
- Admin styles: `admin/assets/css/admin.css`
- Colors and fonts can be customized via CSS variables

### Functionality
- Add new pages by creating PHP files in root directory
- Extend admin functionality in `admin/` directory
- Add new database tables and update `includes/functions.php`

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running
   - Verify database exists

2. **File Upload Issues**
   - Check directory permissions (755 for uploads/)
   - Verify PHP upload settings (upload_max_filesize, post_max_size)
   - Ensure uploads directory exists

3. **Admin Login Issues**
   - Verify database connection
   - Check if users table exists and has data
   - Clear browser cache and cookies

4. **Images Not Displaying**
   - Check file paths in database
   - Verify image files exist in uploads directory
   - Check web server permissions

### Error Logging

Enable error logging in `config/config.php`:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'error.log');
```

## Support

For technical support or customization requests, please contact the development team.

## License

This project is proprietary software developed for Flori Construction Ltd.

---

**Note**: This website is designed specifically for Flori Construction Ltd and includes sample data. Customize the content, images, and branding according to your requirements.
