# Flori Construction Ltd Website - Setup Guide

## Quick Start

### 1. Database Setup
1. Create a MySQL database named `flori_construction`
2. Import the database schema:
   ```bash
   mysql -u username -p flori_construction < database.sql
   ```

### 2. Configuration
1. Edit `config/database.php` with your database credentials
2. Edit `config/config.php` with your site URL
3. Set proper file permissions:
   ```bash
   chmod 755 uploads/
   chmod 755 uploads/services/
   chmod 755 uploads/projects/
   chmod 755 uploads/gallery/
   ```

### 3. Admin Access
- URL: `/admin/login.php`
- Username: `admin`
- Password: `admin123`

**Important**: Change the default password immediately!

## File Structure

```
flori-con/
├── admin/                  # Admin panel
│   ├── assets/            # Admin CSS/JS
│   ├── includes/          # Admin includes
│   ├── dashboard.php      # Admin dashboard
│   ├── login.php          # Admin login
│   └── logout.php         # Admin logout
├── assets/                # Frontend assets
│   ├── css/              # Stylesheets
│   │   └── style.css     # Main stylesheet
│   ├── js/               # JavaScript files
│   │   └── main.js       # Main JavaScript
│   └── images/           # Static images
├── config/                # Configuration files
│   ├── config.php        # Main configuration
│   └── database.php      # Database configuration
├── includes/              # Frontend includes
│   ├── functions.php     # Common functions
│   ├── header.php        # Site header
│   └── footer.php        # Site footer
├── uploads/               # User uploaded files
│   ├── services/         # Service images
│   ├── projects/         # Project images
│   └── gallery/          # Gallery images
├── index.php             # Homepage
├── about.php             # About page
├── services.php          # Services page
├── service.php           # Individual service page
├── projects.php          # Projects page
├── project.php           # Individual project page
├── gallery.php           # Gallery page
├── contact.php           # Contact page
├── database.sql          # Database schema
├── .htaccess             # Apache configuration
├── README.md             # Documentation
└── SETUP_GUIDE.md        # This file
```

## Features Implemented

### Frontend Features ✅
- [x] Modern responsive design
- [x] Homepage with hero section
- [x] About us page with company information
- [x] Services page with detailed service listings
- [x] Individual service pages
- [x] Projects portfolio with categories
- [x] Individual project pages with galleries
- [x] Photo gallery with lightbox
- [x] Contact page with form
- [x] Mobile-responsive navigation
- [x] Social media integration
- [x] SEO-friendly URLs

### Admin Panel Features ✅
- [x] Secure admin login system
- [x] Dashboard with statistics
- [x] Content management system
- [x] Services management (CRUD)
- [x] Projects management (CRUD)
- [x] Image upload system
- [x] Contact form submissions
- [x] Settings management
- [x] User-friendly interface

### Technical Features ✅
- [x] PHP 7.4+ compatible
- [x] MySQL database integration
- [x] Secure authentication
- [x] SQL injection protection
- [x] XSS protection
- [x] File upload security
- [x] Responsive design
- [x] Clean URL structure
- [x] Error handling

## Next Steps

### Required for Production
1. **Add Real Images**: Replace placeholder images with actual project photos
2. **Update Content**: Customize all text content for the company
3. **SSL Certificate**: Install SSL certificate for HTTPS
4. **Email Configuration**: Set up email sending for contact forms
5. **Backup System**: Implement regular database backups
6. **Security Hardening**: Additional security measures for production

### Optional Enhancements
1. **SEO Optimization**: Add meta tags, sitemap, robots.txt
2. **Analytics**: Integrate Google Analytics
3. **Performance**: Optimize images and enable caching
4. **Additional Features**: Blog, testimonials, team pages
5. **Multi-language**: Add language support if needed

## Admin Panel Usage

### Adding Services
1. Login to admin panel
2. Go to Services section
3. Click "Add New Service"
4. Fill in service details and upload image
5. Save changes

### Adding Projects
1. Go to Projects section
2. Click "Add New Project"
3. Fill in project details
4. Upload featured image and gallery images
5. Set project category (completed/ongoing)
6. Save changes

### Managing Content
1. Go to Content Management
2. Edit homepage sections
3. Update company information
4. Modify about us content

### Viewing Contact Submissions
1. Go to Contact Messages
2. View new submissions
3. Mark as read/replied
4. Export if needed

## Troubleshooting

### Common Issues
1. **Database Connection Error**: Check credentials in `config/database.php`
2. **Images Not Loading**: Check file permissions and paths
3. **Admin Login Issues**: Verify database connection and user table
4. **Upload Errors**: Check directory permissions and PHP settings

### Support
For technical support or customization requests, refer to the main README.md file.

## Security Notes

- Change default admin password immediately
- Keep PHP and MySQL updated
- Regular security updates
- Monitor file uploads
- Use HTTPS in production
- Regular backups

---

**Website Status**: ✅ Ready for deployment and customization
**Last Updated**: December 2024
