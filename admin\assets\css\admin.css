/* Admin Panel Styles */

:root {
    --admin-primary: #2c3e50;
    --admin-secondary: #e67e22;
    --admin-success: #27ae60;
    --admin-danger: #e74c3c;
    --admin-warning: #f39c12;
    --admin-info: #3498db;
    --admin-light: #ecf0f1;
    --admin-dark: #1a252f;
    --admin-sidebar-width: 260px;
    --admin-header-height: 70px;
}

/* Login Page */
.login-page {
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-dark));
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Inter', sans-serif;
}

.login-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
}

.login-card {
    background: white;
    border-radius: 10px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo {
    height: 60px;
    margin-bottom: 20px;
}

.login-header h2 {
    color: var(--admin-primary);
    margin-bottom: 10px;
}

.login-header p {
    color: #666;
    margin-bottom: 0;
}

.login-form .form-label {
    font-weight: 500;
    color: var(--admin-primary);
}

.login-form .input-group-text {
    background-color: var(--admin-light);
    border-color: #ddd;
}

.login-form .form-control {
    border-color: #ddd;
    padding: 12px;
}

.login-form .form-control:focus {
    border-color: var(--admin-secondary);
    box-shadow: 0 0 0 0.2rem rgba(230, 126, 34, 0.25);
}

.login-footer {
    text-align: center;
    margin-top: 20px;
}

.login-footer a {
    color: var(--admin-primary);
    text-decoration: none;
}

/* Admin Layout */
.admin-body {
    font-family: 'Inter', sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

.admin-wrapper {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.admin-sidebar {
    width: var(--admin-sidebar-width);
    background-color: var(--admin-primary);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-logo {
    height: 40px;
    margin-bottom: 10px;
}

.sidebar-header h5 {
    margin: 0;
    font-weight: 500;
    opacity: 0.9;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu-item {
    margin: 0;
}

.menu-item a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.menu-item a:hover,
.menu-item.active a {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

.menu-item.active a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: var(--admin-secondary);
}

.menu-item a i {
    width: 20px;
    margin-right: 12px;
    text-align: center;
}

.menu-item .badge {
    margin-left: auto;
    font-size: 11px;
}

.menu-divider {
    height: 1px;
    background-color: rgba(255,255,255,0.1);
    margin: 10px 0;
}

/* Main Content */
.admin-main {
    flex: 1;
    margin-left: var(--admin-sidebar-width);
    display: flex;
    flex-direction: column;
}

/* Header */
.admin-header {
    height: var(--admin-header-height);
    background-color: white;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    position: sticky;
    top: 0;
    z-index: 999;
}

.header-left .sidebar-toggle {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--admin-primary);
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.header-left .sidebar-toggle:hover {
    background-color: var(--admin-light);
}

.header-right {
    display: flex;
    align-items: center;
}

.header-user {
    display: flex;
    align-items: center;
}

.user-info {
    text-align: right;
    margin-right: 12px;
}

.user-name {
    display: block;
    font-weight: 500;
    color: var(--admin-primary);
    font-size: 14px;
}

.user-role {
    display: block;
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background-color: var(--admin-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--admin-primary);
    font-size: 20px;
}

/* Content Area */
.admin-content {
    flex: 1;
    padding: 30px;
}

.content-header {
    margin-bottom: 30px;
}

.content-header h1 {
    color: var(--admin-primary);
    margin-bottom: 5px;
    font-size: 28px;
    font-weight: 600;
}

.content-header p {
    color: #666;
    margin: 0;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: white;
    border-bottom: 1px solid #eee;
    padding: 20px;
    display: flex;
    justify-content: between;
    align-items: center;
}

.card-title {
    margin: 0;
    font-weight: 600;
    color: var(--admin-primary);
}

.card-body {
    padding: 20px;
}

/* Statistics Cards */
.stat-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    color: white;
    font-size: 24px;
}

.stat-icon.bg-primary { background-color: var(--admin-primary); }
.stat-icon.bg-success { background-color: var(--admin-success); }
.stat-icon.bg-info { background-color: var(--admin-info); }
.stat-icon.bg-warning { background-color: var(--admin-warning); }

.stat-content h3 {
    font-size: 32px;
    font-weight: 700;
    margin: 0;
    color: var(--admin-primary);
}

.stat-content p {
    margin: 0;
    color: #666;
    font-weight: 500;
}

/* Quick Actions */
.quick-action-card {
    display: block;
    background: white;
    border-radius: 10px;
    padding: 25px;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    text-align: center;
    height: 100%;
}

.quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    color: inherit;
    text-decoration: none;
}

.quick-action-icon {
    width: 60px;
    height: 60px;
    background-color: var(--admin-secondary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 24px;
}

.quick-action-card h6 {
    color: var(--admin-primary);
    font-weight: 600;
    margin-bottom: 10px;
}

.quick-action-card p {
    color: #666;
    margin: 0;
    font-size: 14px;
}

/* Forms */
.form-label {
    font-weight: 500;
    color: var(--admin-primary);
    margin-bottom: 8px;
}

.form-control {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 12px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--admin-secondary);
    box-shadow: 0 0 0 0.2rem rgba(230, 126, 34, 0.25);
}

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--admin-secondary);
    border-color: var(--admin-secondary);
}

.btn-primary:hover {
    background-color: #d35400;
    border-color: #d35400;
    transform: translateY(-1px);
}

/* Tables */
.table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table th {
    background-color: var(--admin-light);
    border: none;
    font-weight: 600;
    color: var(--admin-primary);
    padding: 15px;
}

.table td {
    border: none;
    padding: 15px;
    vertical-align: middle;
}

.table tbody tr {
    border-bottom: 1px solid #eee;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Responsive */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-sidebar.show {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-left: 0;
    }
    
    .admin-content {
        padding: 20px;
    }
    
    .user-info {
        display: none;
    }
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-dark));
    color: white;
    padding: 100px 0 60px;
    margin-top: 80px;
}

.page-header-content {
    text-align: center;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    justify-content: center;
}

.breadcrumb-item a {
    color: rgba(255,255,255,0.8);
}

.breadcrumb-item.active {
    color: white;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}
