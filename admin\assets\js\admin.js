/**
 * Admin Panel JavaScript
 * Flori Construction Ltd
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize admin components
    initSidebar();
    initDataTables();
    initImageUpload();
    initFormValidation();
    initConfirmDialogs();
    
    // Sidebar functionality
    function initSidebar() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.querySelector('.admin-sidebar');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });
            
            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 768) {
                    if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                        sidebar.classList.remove('show');
                    }
                }
            });
        }
    }
    
    // Initialize DataTables
    function initDataTables() {
        const tables = document.querySelectorAll('.data-table');
        
        tables.forEach(table => {
            // Simple table enhancement
            addTableSearch(table);
            addTableSorting(table);
        });
    }
    
    // Add search functionality to tables
    function addTableSearch(table) {
        const searchInput = table.parentElement.querySelector('.table-search');
        if (!searchInput) return;
        
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });
    }
    
    // Add sorting functionality to tables
    function addTableSorting(table) {
        const headers = table.querySelectorAll('th[data-sort]');
        
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                const column = this.dataset.sort;
                const tbody = table.querySelector('tbody');
                const rows = Array.from(tbody.querySelectorAll('tr'));
                const isAscending = this.classList.contains('sort-asc');
                
                // Remove existing sort classes
                headers.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
                
                // Add new sort class
                this.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
                
                // Sort rows
                rows.sort((a, b) => {
                    const aValue = a.querySelector(`td:nth-child(${parseInt(column) + 1})`).textContent;
                    const bValue = b.querySelector(`td:nth-child(${parseInt(column) + 1})`).textContent;
                    
                    if (isAscending) {
                        return bValue.localeCompare(aValue);
                    } else {
                        return aValue.localeCompare(bValue);
                    }
                });
                
                // Reorder DOM
                rows.forEach(row => tbody.appendChild(row));
            });
        });
    }
    
    // Image upload functionality
    function initImageUpload() {
        const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
        
        imageInputs.forEach(input => {
            input.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    // Validate file size (5MB max)
                    if (file.size > 5 * 1024 * 1024) {
                        alert('File size must be less than 5MB');
                        this.value = '';
                        return;
                    }
                    
                    // Validate file type
                    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                    if (!allowedTypes.includes(file.type)) {
                        alert('Please select a valid image file (JPEG, PNG, GIF, WebP)');
                        this.value = '';
                        return;
                    }
                    
                    // Show preview
                    showImagePreview(this, file);
                }
            });
        });
    }
    
    // Show image preview
    function showImagePreview(input, file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            let preview = input.parentElement.querySelector('.image-preview');
            
            if (!preview) {
                preview = document.createElement('div');
                preview.className = 'image-preview mt-2';
                input.parentElement.appendChild(preview);
            }
            
            preview.innerHTML = `
                <img src="${e.target.result}" alt="Preview" style="max-width: 200px; max-height: 200px; border-radius: 8px;">
                <button type="button" class="btn btn-sm btn-danger mt-2" onclick="removeImagePreview(this)">Remove</button>
            `;
        };
        reader.readAsDataURL(file);
    }
    
    // Form validation
    function initFormValidation() {
        const forms = document.querySelectorAll('.needs-validation');
        
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
        
        // Real-time validation
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
        });
    }
    
    // Validate individual field
    function validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        const required = field.hasAttribute('required');
        
        // Clear previous validation
        field.classList.remove('is-valid', 'is-invalid');
        
        // Required field validation
        if (required && !value) {
            field.classList.add('is-invalid');
            return false;
        }
        
        // Email validation
        if (type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                field.classList.add('is-invalid');
                return false;
            }
        }
        
        // URL validation
        if (type === 'url' && value) {
            try {
                new URL(value);
            } catch {
                field.classList.add('is-invalid');
                return false;
            }
        }
        
        // Phone validation
        if (type === 'tel' && value) {
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            if (!phoneRegex.test(value.replace(/\s/g, ''))) {
                field.classList.add('is-invalid');
                return false;
            }
        }
        
        if (value || !required) {
            field.classList.add('is-valid');
        }
        
        return true;
    }
    
    // Confirm dialogs
    function initConfirmDialogs() {
        const deleteButtons = document.querySelectorAll('[data-confirm]');
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                const message = this.dataset.confirm || 'Are you sure you want to delete this item?';
                if (!confirm(message)) {
                    e.preventDefault();
                }
            });
        });
    }
    
    // WYSIWYG Editor (simple implementation)
    function initWYSIWYG() {
        const textareas = document.querySelectorAll('.wysiwyg');
        
        textareas.forEach(textarea => {
            // Create toolbar
            const toolbar = document.createElement('div');
            toolbar.className = 'wysiwyg-toolbar';
            toolbar.innerHTML = `
                <button type="button" data-command="bold"><i class="fas fa-bold"></i></button>
                <button type="button" data-command="italic"><i class="fas fa-italic"></i></button>
                <button type="button" data-command="underline"><i class="fas fa-underline"></i></button>
                <button type="button" data-command="insertUnorderedList"><i class="fas fa-list-ul"></i></button>
                <button type="button" data-command="insertOrderedList"><i class="fas fa-list-ol"></i></button>
            `;
            
            // Insert toolbar before textarea
            textarea.parentElement.insertBefore(toolbar, textarea);
            
            // Convert textarea to contenteditable div
            const editor = document.createElement('div');
            editor.className = 'wysiwyg-editor form-control';
            editor.contentEditable = true;
            editor.innerHTML = textarea.value;
            editor.style.minHeight = '150px';
            
            textarea.style.display = 'none';
            textarea.parentElement.insertBefore(editor, textarea.nextSibling);
            
            // Toolbar functionality
            toolbar.addEventListener('click', function(e) {
                if (e.target.closest('[data-command]')) {
                    e.preventDefault();
                    const command = e.target.closest('[data-command]').dataset.command;
                    document.execCommand(command, false, null);
                    editor.focus();
                }
            });
            
            // Update textarea on change
            editor.addEventListener('input', function() {
                textarea.value = this.innerHTML;
            });
        });
    }
    
    // Initialize WYSIWYG if needed
    if (document.querySelector('.wysiwyg')) {
        initWYSIWYG();
    }
    
});

// Global functions
function removeImagePreview(button) {
    const preview = button.parentElement;
    const input = preview.parentElement.querySelector('input[type="file"]');
    input.value = '';
    preview.remove();
}

function toggleStatus(id, type) {
    if (confirm('Are you sure you want to change the status?')) {
        // AJAX call to update status
        fetch(`${window.location.pathname}?action=toggle_status&id=${id}&type=${type}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error updating status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating status');
        });
    }
}

function bulkAction(action) {
    const checkboxes = document.querySelectorAll('input[name="selected[]"]:checked');
    
    if (checkboxes.length === 0) {
        alert('Please select at least one item');
        return;
    }
    
    if (confirm(`Are you sure you want to ${action} the selected items?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = window.location.pathname;
        
        // Add action input
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'bulk_action';
        actionInput.value = action;
        form.appendChild(actionInput);
        
        // Add selected IDs
        checkboxes.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'selected[]';
            input.value = checkbox.value;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Auto-save functionality
function initAutoSave() {
    const forms = document.querySelectorAll('[data-autosave]');
    
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            input.addEventListener('input', debounce(function() {
                autoSave(form);
            }, 2000));
        });
    });
}

function autoSave(form) {
    const formData = new FormData(form);
    formData.append('autosave', '1');
    
    fetch(form.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Changes saved automatically', 'success');
        }
    })
    .catch(error => {
        console.error('Auto-save error:', error);
    });
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification`;
    notification.innerHTML = `
        <span>${message}</span>
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
