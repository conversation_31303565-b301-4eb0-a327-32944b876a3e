<?php
require_once '../includes/functions.php';
require_admin_login();

$page_title = 'Contact Messages';
$success_message = '';
$error_message = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        $db = getDB();
        
        if ($action === 'mark_read' && isset($_POST['id'])) {
            $id = (int)$_POST['id'];
            $stmt = $db->prepare("UPDATE contact_submissions SET status = 'read' WHERE id = ?");
            if ($stmt->execute([$id])) {
                $success_message = 'Message marked as read.';
            }
        } elseif ($action === 'mark_replied' && isset($_POST['id'])) {
            $id = (int)$_POST['id'];
            $stmt = $db->prepare("UPDATE contact_submissions SET status = 'replied' WHERE id = ?");
            if ($stmt->execute([$id])) {
                $success_message = 'Message marked as replied.';
            }
        } elseif ($action === 'delete' && isset($_POST['id'])) {
            $id = (int)$_POST['id'];
            $stmt = $db->prepare("DELETE FROM contact_submissions WHERE id = ?");
            if ($stmt->execute([$id])) {
                $success_message = 'Message deleted successfully.';
            }
        } elseif ($action === 'bulk_action' && isset($_POST['selected']) && isset($_POST['bulk_action'])) {
            $selected_ids = array_map('intval', $_POST['selected']);
            $bulk_action = $_POST['bulk_action'];
            
            if (!empty($selected_ids)) {
                $placeholders = str_repeat('?,', count($selected_ids) - 1) . '?';
                
                if ($bulk_action === 'mark_read') {
                    $stmt = $db->prepare("UPDATE contact_submissions SET status = 'read' WHERE id IN ($placeholders)");
                    if ($stmt->execute($selected_ids)) {
                        $success_message = count($selected_ids) . ' messages marked as read.';
                    }
                } elseif ($bulk_action === 'mark_replied') {
                    $stmt = $db->prepare("UPDATE contact_submissions SET status = 'replied' WHERE id IN ($placeholders)");
                    if ($stmt->execute($selected_ids)) {
                        $success_message = count($selected_ids) . ' messages marked as replied.';
                    }
                } elseif ($bulk_action === 'delete') {
                    $stmt = $db->prepare("DELETE FROM contact_submissions WHERE id IN ($placeholders)");
                    if ($stmt->execute($selected_ids)) {
                        $success_message = count($selected_ids) . ' messages deleted.';
                    }
                }
            }
        }
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';

// Build query
$db = getDB();
$where_conditions = [];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
$sql = "SELECT * FROM contact_submissions $where_clause ORDER BY created_at DESC";

$stmt = $db->prepare($sql);
$stmt->execute($params);
$contacts = $stmt->fetchAll();

// Get statistics
$stats_sql = "SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN status = 'new' THEN 1 ELSE 0 END) as new_count,
    SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as read_count,
    SUM(CASE WHEN status = 'replied' THEN 1 ELSE 0 END) as replied_count
    FROM contact_submissions";
$stats = $db->query($stats_sql)->fetch();

include_admin_header($page_title);
?>

<div class="admin-content">
    <div class="content-header">
        <h1>Contact Messages</h1>
        <p>Manage contact form submissions and inquiries</p>
    </div>
    
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $stats['total']; ?></h3>
                    <p>Total Messages</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-envelope-open"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $stats['new_count']; ?></h3>
                    <p>New Messages</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $stats['read_count']; ?></h3>
                    <p>Read Messages</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-reply"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $stats['replied_count']; ?></h3>
                    <p>Replied Messages</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row align-items-end">
                <div class="col-md-3">
                    <label class="form-label">Filter by Status</label>
                    <select name="status" class="form-control">
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>All Messages</option>
                        <option value="new" <?php echo $status_filter === 'new' ? 'selected' : ''; ?>>New</option>
                        <option value="read" <?php echo $status_filter === 'read' ? 'selected' : ''; ?>>Read</option>
                        <option value="replied" <?php echo $status_filter === 'replied' ? 'selected' : ''; ?>>Replied</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Search Messages</label>
                    <input type="text" name="search" class="form-control" placeholder="Search by name, email, subject, or message..." value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="contacts.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Messages Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title">Contact Messages</h5>
                <div class="bulk-actions" style="display: none;">
                    <select id="bulkActionSelect" class="form-select form-select-sm d-inline-block w-auto me-2">
                        <option value="">Bulk Actions</option>
                        <option value="mark_read">Mark as Read</option>
                        <option value="mark_replied">Mark as Replied</option>
                        <option value="delete">Delete</option>
                    </select>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="applyBulkAction()">Apply</button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($contacts)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5>No Messages Found</h5>
                    <p class="text-muted">
                        <?php if (!empty($search) || $status_filter !== 'all'): ?>
                            No messages match your current filters.
                        <?php else: ?>
                            No contact messages have been received yet.
                        <?php endif; ?>
                    </p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="30">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>Contact Info</th>
                                <th>Subject</th>
                                <th>Message</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($contacts as $contact): ?>
                                <tr class="<?php echo $contact['status'] === 'new' ? 'table-warning' : ''; ?>">
                                    <td>
                                        <input type="checkbox" name="selected[]" value="<?php echo $contact['id']; ?>" class="form-check-input contact-checkbox">
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($contact['name']); ?></strong><br>
                                        <a href="mailto:<?php echo htmlspecialchars($contact['email']); ?>" class="text-muted">
                                            <?php echo htmlspecialchars($contact['email']); ?>
                                        </a>
                                        <?php if ($contact['phone']): ?>
                                            <br><small class="text-muted">
                                                <i class="fas fa-phone"></i> <?php echo htmlspecialchars($contact['phone']); ?>
                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo htmlspecialchars($contact['subject'] ?: 'No Subject'); ?>
                                    </td>
                                    <td>
                                        <div class="message-preview">
                                            <?php echo htmlspecialchars(substr($contact['message'], 0, 100)); ?>
                                            <?php if (strlen($contact['message']) > 100): ?>...<?php endif; ?>
                                        </div>
                                        <?php if (strlen($contact['message']) > 100): ?>
                                            <button class="btn btn-link btn-sm p-0" data-bs-toggle="modal" data-bs-target="#messageModal" 
                                                    data-message="<?php echo htmlspecialchars($contact['message']); ?>"
                                                    data-name="<?php echo htmlspecialchars($contact['name']); ?>"
                                                    data-subject="<?php echo htmlspecialchars($contact['subject']); ?>">
                                                Read Full Message
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $contact['status'] === 'new' ? 'warning' : 
                                                ($contact['status'] === 'read' ? 'info' : 'success'); 
                                        ?>">
                                            <?php echo ucfirst($contact['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small>
                                            <?php echo format_date($contact['created_at'], 'M j, Y'); ?><br>
                                            <?php echo format_date($contact['created_at'], 'g:i A'); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="mailto:<?php echo htmlspecialchars($contact['email']); ?>?subject=Re: <?php echo urlencode($contact['subject']); ?>" 
                                               class="btn btn-outline-primary" title="Reply">
                                                <i class="fas fa-reply"></i>
                                            </a>
                                            <?php if ($contact['status'] === 'new'): ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="mark_read">
                                                    <input type="hidden" name="id" value="<?php echo $contact['id']; ?>">
                                                    <button type="submit" class="btn btn-outline-info" title="Mark as Read">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                            <?php if ($contact['status'] !== 'replied'): ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="mark_replied">
                                                    <input type="hidden" name="id" value="<?php echo $contact['id']; ?>">
                                                    <button type="submit" class="btn btn-outline-success" title="Mark as Replied">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                            <button class="btn btn-outline-danger delete-contact" 
                                                    data-id="<?php echo $contact['id']; ?>" 
                                                    data-name="<?php echo htmlspecialchars($contact['name']); ?>" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Message Modal -->
<div class="modal fade" id="messageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Full Message</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>From:</strong> <span id="modalName"></span><br>
                    <strong>Subject:</strong> <span id="modalSubject"></span>
                </div>
                <div class="border-top pt-3">
                    <p id="modalMessage" style="white-space: pre-wrap;"></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="replyButton">
                    <i class="fas fa-reply"></i> Reply
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the message from "<span id="deleteContactName"></span>"?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="deleteContactId">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Message
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all functionality
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.contact-checkbox');
    const bulkActions = document.querySelector('.bulk-actions');
    
    selectAll.addEventListener('change', function() {
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        toggleBulkActions();
    });
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', toggleBulkActions);
    });
    
    function toggleBulkActions() {
        const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked');
        if (checkedBoxes.length > 0) {
            bulkActions.style.display = 'block';
        } else {
            bulkActions.style.display = 'none';
        }
    }
    
    // Message modal
    const messageModal = document.getElementById('messageModal');
    messageModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const message = button.getAttribute('data-message');
        const name = button.getAttribute('data-name');
        const subject = button.getAttribute('data-subject');
        
        document.getElementById('modalName').textContent = name;
        document.getElementById('modalSubject').textContent = subject || 'No Subject';
        document.getElementById('modalMessage').textContent = message;
        
        // Set up reply button
        const replyButton = document.getElementById('replyButton');
        replyButton.onclick = function() {
            const email = button.closest('tr').querySelector('a[href^="mailto:"]').href;
            window.location.href = email;
        };
    });
    
    // Delete contact
    document.querySelectorAll('.delete-contact').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            
            document.getElementById('deleteContactId').value = id;
            document.getElementById('deleteContactName').textContent = name;
            
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        });
    });
});

function applyBulkAction() {
    const action = document.getElementById('bulkActionSelect').value;
    const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked');
    
    if (!action) {
        alert('Please select an action.');
        return;
    }
    
    if (checkedBoxes.length === 0) {
        alert('Please select at least one message.');
        return;
    }
    
    if (action === 'delete' && !confirm('Are you sure you want to delete the selected messages?')) {
        return;
    }
    
    // Create and submit form
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `<input type="hidden" name="action" value="bulk_action">
                      <input type="hidden" name="bulk_action" value="${action}">`;
    
    checkedBoxes.forEach(checkbox => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'selected[]';
        input.value = checkbox.value;
        form.appendChild(input);
    });
    
    document.body.appendChild(form);
    form.submit();
}
</script>

<?php include_admin_footer(); ?>
