<?php
require_once '../includes/functions.php';
require_admin_login();

$page_title = 'Content Management';
$success_message = '';
$error_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'update_content') {
        $updates = 0;
        
        foreach ($_POST['content'] as $page => $sections) {
            foreach ($sections as $section => $keys) {
                foreach ($keys as $key => $value) {
                    $content_type = $_POST['content_type'][$page][$section][$key] ?? 'text';
                    if (update_content($page, $section, $key, $value, $content_type)) {
                        $updates++;
                    }
                }
            }
        }
        
        if ($updates > 0) {
            $success_message = "Successfully updated $updates content items!";
        } else {
            $error_message = 'No content was updated.';
        }
    }
}

// Get all content organized by page and section
$db = getDB();
$stmt = $db->query("SELECT * FROM content ORDER BY page, section, content_key");
$all_content = $stmt->fetchAll();

$content_by_page = [];
foreach ($all_content as $item) {
    $content_by_page[$item['page']][$item['section']][$item['content_key']] = $item;
}

include_admin_header($page_title);
?>

<div class="admin-content">
    <div class="content-header">
        <h1>Content Management</h1>
        <p>Edit website content and text that appears on your pages</p>
    </div>
    
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <form method="POST" id="contentForm">
        <input type="hidden" name="action" value="update_content">
        
        <!-- Homepage Content -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-home"></i> Homepage Content
                </h5>
            </div>
            <div class="card-body">
                <!-- Hero Section -->
                <h6 class="text-primary mb-3">Hero Section</h6>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Hero Title</label>
                        <input type="text" class="form-control" 
                               name="content[home][hero][title]" 
                               value="<?php echo htmlspecialchars($content_by_page['home']['hero']['title']['content_value'] ?? ''); ?>">
                        <input type="hidden" name="content_type[home][hero][title]" value="text">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Hero Subtitle</label>
                        <input type="text" class="form-control" 
                               name="content[home][hero][subtitle]" 
                               value="<?php echo htmlspecialchars($content_by_page['home']['hero']['subtitle']['content_value'] ?? ''); ?>">
                        <input type="hidden" name="content_type[home][hero][subtitle]" value="text">
                    </div>
                </div>
                
                <!-- About Section -->
                <h6 class="text-primary mb-3">About Section</h6>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">About Title</label>
                        <input type="text" class="form-control" 
                               name="content[home][about][title]" 
                               value="<?php echo htmlspecialchars($content_by_page['home']['about']['title']['content_value'] ?? ''); ?>">
                        <input type="hidden" name="content_type[home][about][title]" value="text">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">About Description</label>
                        <textarea class="form-control" rows="3" 
                                  name="content[home][about][description]"><?php echo htmlspecialchars($content_by_page['home']['about']['description']['content_value'] ?? ''); ?></textarea>
                        <input type="hidden" name="content_type[home][about][description]" value="text">
                    </div>
                </div>
                
                <!-- Services Section -->
                <h6 class="text-primary mb-3">Services Section</h6>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Services Title</label>
                        <input type="text" class="form-control" 
                               name="content[home][services][title]" 
                               value="<?php echo htmlspecialchars($content_by_page['home']['services']['title']['content_value'] ?? ''); ?>">
                        <input type="hidden" name="content_type[home][services][title]" value="text">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Services Subtitle</label>
                        <textarea class="form-control" rows="2" 
                                  name="content[home][services][subtitle]"><?php echo htmlspecialchars($content_by_page['home']['services']['subtitle']['content_value'] ?? ''); ?></textarea>
                        <input type="hidden" name="content_type[home][services][subtitle]" value="text">
                    </div>
                </div>
                
                <!-- Projects Section -->
                <h6 class="text-primary mb-3">Projects Section</h6>
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Projects Title</label>
                        <input type="text" class="form-control" 
                               name="content[home][projects][title]" 
                               value="<?php echo htmlspecialchars($content_by_page['home']['projects']['title']['content_value'] ?? ''); ?>">
                        <input type="hidden" name="content_type[home][projects][title]" value="text">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Projects Subtitle</label>
                        <textarea class="form-control" rows="2" 
                                  name="content[home][projects][subtitle]"><?php echo htmlspecialchars($content_by_page['home']['projects']['subtitle']['content_value'] ?? ''); ?></textarea>
                        <input type="hidden" name="content_type[home][projects][subtitle]" value="text">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- About Page Content -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-info-circle"></i> About Page Content
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label class="form-label">Company Story</label>
                        <textarea class="form-control" rows="4" 
                                  name="content[about][story][description]"><?php echo htmlspecialchars($content_by_page['about']['story']['description']['content_value'] ?? 'Flori Construction Ltd began its journey in the heart of London, founded on the principles of quality, professionalism, and an unwavering dedication to customer satisfaction.'); ?></textarea>
                        <input type="hidden" name="content_type[about][story][description]" value="text">
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Mission Statement</label>
                        <textarea class="form-control" rows="3" 
                                  name="content[about][mission][statement]"><?php echo htmlspecialchars($content_by_page['about']['mission']['statement']['content_value'] ?? 'To deliver exceptional construction services that exceed our clients\' expectations while maintaining the highest standards of quality, safety, and professionalism.'); ?></textarea>
                        <input type="hidden" name="content_type[about][mission][statement]" value="text">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Vision Statement</label>
                        <textarea class="form-control" rows="3" 
                                  name="content[about][vision][statement]"><?php echo htmlspecialchars($content_by_page['about']['vision']['statement']['content_value'] ?? 'To be London\'s leading construction company, recognized for our innovation, sustainability, and commitment to excellence.'); ?></textarea>
                        <input type="hidden" name="content_type[about][vision][statement]" value="text">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Contact Page Content -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-envelope"></i> Contact Page Content
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Contact Page Title</label>
                        <input type="text" class="form-control" 
                               name="content[contact][header][title]" 
                               value="<?php echo htmlspecialchars($content_by_page['contact']['header']['title']['content_value'] ?? 'Let\'s Discuss Your Project'); ?>">
                        <input type="hidden" name="content_type[contact][header][title]" value="text">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Contact Page Subtitle</label>
                        <textarea class="form-control" rows="2" 
                                  name="content[contact][header][subtitle]"><?php echo htmlspecialchars($content_by_page['contact']['header']['subtitle']['content_value'] ?? 'Ready to start your construction project? Contact us today for a free consultation and quote.'); ?></textarea>
                        <input type="hidden" name="content_type[contact][header][subtitle]" value="text">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Call-to-Action Content -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-bullhorn"></i> Call-to-Action Content
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">CTA Title</label>
                        <input type="text" class="form-control" 
                               name="content[cta][main][title]" 
                               value="<?php echo htmlspecialchars($content_by_page['cta']['main']['title']['content_value'] ?? 'Ready to Start Your Construction Project?'); ?>">
                        <input type="hidden" name="content_type[cta][main][title]" value="text">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">CTA Description</label>
                        <textarea class="form-control" rows="2" 
                                  name="content[cta][main][description]"><?php echo htmlspecialchars($content_by_page['cta']['main']['description']['content_value'] ?? 'We Collaborate with Ambitious Brands and People; We\'d Love to Build Something Great Together.'); ?></textarea>
                        <input type="hidden" name="content_type[cta][main][description]" value="text">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Save Button -->
        <div class="d-flex justify-content-end">
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save"></i> Save All Content
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-save functionality (optional)
    let saveTimeout;
    const form = document.getElementById('contentForm');
    const inputs = form.querySelectorAll('input, textarea');
    
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(saveTimeout);
            
            // Show saving indicator
            const saveBtn = document.querySelector('button[type="submit"]');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Auto-saving...';
            saveBtn.disabled = true;
            
            saveTimeout = setTimeout(() => {
                // Auto-save after 2 seconds of no typing
                const formData = new FormData(form);
                
                fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.text())
                .then(data => {
                    // Reset button
                    saveBtn.innerHTML = originalText;
                    saveBtn.disabled = false;
                    
                    // Show success message briefly
                    const tempMsg = document.createElement('div');
                    tempMsg.className = 'alert alert-success position-fixed';
                    tempMsg.style.cssText = 'top: 20px; right: 20px; z-index: 9999; opacity: 0.9;';
                    tempMsg.innerHTML = '<i class="fas fa-check"></i> Content auto-saved';
                    document.body.appendChild(tempMsg);
                    
                    setTimeout(() => {
                        tempMsg.remove();
                    }, 2000);
                })
                .catch(error => {
                    console.error('Auto-save error:', error);
                    saveBtn.innerHTML = originalText;
                    saveBtn.disabled = false;
                });
            }, 2000);
        });
    });
    
    // Form validation
    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('input[required], textarea[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
});
</script>

<?php include_admin_footer(); ?>
