<?php
require_once '../includes/functions.php';
require_admin_login();

$page_title = 'Dashboard';

// Get statistics
$db = getDB();

// Count services
$stmt = $db->query("SELECT COUNT(*) as count FROM services WHERE status = 'active'");
$services_count = $stmt->fetch()['count'];

// Count projects
$stmt = $db->query("SELECT COUNT(*) as count FROM projects WHERE status = 'active'");
$projects_count = $stmt->fetch()['count'];

// Count completed projects
$stmt = $db->query("SELECT COUNT(*) as count FROM projects WHERE status = 'active' AND category = 'completed'");
$completed_projects = $stmt->fetch()['count'];

// Count ongoing projects
$stmt = $db->query("SELECT COUNT(*) as count FROM projects WHERE status = 'active' AND category = 'ongoing'");
$ongoing_projects = $stmt->fetch()['count'];

// Count contact submissions
$stmt = $db->query("SELECT COUNT(*) as count FROM contact_submissions WHERE status = 'new'");
$new_contacts = $stmt->fetch()['count'];

// Get recent contact submissions
$stmt = $db->query("SELECT * FROM contact_submissions ORDER BY created_at DESC LIMIT 5");
$recent_contacts = $stmt->fetchAll();

// Get recent projects
$stmt = $db->query("SELECT * FROM projects WHERE status = 'active' ORDER BY created_at DESC LIMIT 5");
$recent_projects = $stmt->fetchAll();

include_admin_header($page_title);
?>

<div class="admin-content">
    <div class="content-header">
        <h1>Dashboard</h1>
        <p>Welcome back, <?php echo htmlspecialchars($_SESSION['admin_username']); ?>!</p>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-tools"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $services_count; ?></h3>
                    <p>Active Services</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-building"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $projects_count; ?></h3>
                    <p>Total Projects</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $completed_projects; ?></h3>
                    <p>Completed Projects</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $new_contacts; ?></h3>
                    <p>New Messages</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Recent Contact Submissions -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Recent Contact Submissions</h5>
                    <a href="<?php echo ADMIN_URL; ?>/contacts.php" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_contacts)): ?>
                        <p class="text-muted">No contact submissions yet.</p>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recent_contacts as $contact): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($contact['name']); ?></h6>
                                            <p class="mb-1 text-muted"><?php echo htmlspecialchars($contact['email']); ?></p>
                                            <small class="text-muted"><?php echo htmlspecialchars($contact['subject']); ?></small>
                                        </div>
                                        <small class="text-muted"><?php echo format_date($contact['created_at'], 'M j'); ?></small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Recent Projects -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Recent Projects</h5>
                    <a href="<?php echo ADMIN_URL; ?>/projects.php" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_projects)): ?>
                        <p class="text-muted">No projects added yet.</p>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recent_projects as $project): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($project['title']); ?></h6>
                                            <p class="mb-1 text-muted"><?php echo htmlspecialchars($project['client']); ?></p>
                                            <small class="text-muted">
                                                <span class="badge bg-<?php echo $project['category'] === 'completed' ? 'success' : 'warning'; ?>">
                                                    <?php echo ucfirst($project['category']); ?>
                                                </span>
                                            </small>
                                        </div>
                                        <small class="text-muted"><?php echo format_date($project['created_at'], 'M j'); ?></small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?php echo ADMIN_URL; ?>/projects.php?action=add" class="quick-action-card">
                                <div class="quick-action-icon">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <h6>Add New Project</h6>
                                <p>Create a new project entry</p>
                            </a>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?php echo ADMIN_URL; ?>/services.php?action=add" class="quick-action-card">
                                <div class="quick-action-icon">
                                    <i class="fas fa-tools"></i>
                                </div>
                                <h6>Add New Service</h6>
                                <p>Create a new service offering</p>
                            </a>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?php echo ADMIN_URL; ?>/content.php" class="quick-action-card">
                                <div class="quick-action-icon">
                                    <i class="fas fa-edit"></i>
                                </div>
                                <h6>Edit Content</h6>
                                <p>Update website content</p>
                            </a>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="<?php echo ADMIN_URL; ?>/settings.php" class="quick-action-card">
                                <div class="quick-action-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <h6>Settings</h6>
                                <p>Configure website settings</p>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_admin_footer(); ?>
