<?php
require_once '../includes/functions.php';
require_admin_login();

$page_title = 'Gallery Management';
$success_message = '';
$error_message = '';

// Get project ID if specified
$project_id = isset($_GET['project_id']) ? (int)$_GET['project_id'] : null;
$project = null;

if ($project_id) {
    $project = get_project_by_id($project_id);
    if (!$project) {
        redirect(ADMIN_URL . '/projects.php');
    }
    $page_title = 'Gallery - ' . $project['title'];
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        $db = getDB();
        
        if ($action === 'add_images' && $project_id) {
            if (isset($_FILES['gallery_images'])) {
                $uploaded_count = 0;
                
                foreach ($_FILES['gallery_images']['tmp_name'] as $key => $tmp_name) {
                    if ($_FILES['gallery_images']['error'][$key] === UPLOAD_ERR_OK) {
                        $gallery_file = [
                            'tmp_name' => $tmp_name,
                            'name' => $_FILES['gallery_images']['name'][$key],
                            'size' => $_FILES['gallery_images']['size'][$key],
                            'type' => $_FILES['gallery_images']['type'][$key]
                        ];
                        
                        $upload_result = upload_image($gallery_file, 'projects');
                        if ($upload_result['success']) {
                            $caption = $_POST['gallery_captions'][$key] ?? '';
                            $sort_order = (int)($_POST['sort_orders'][$key] ?? 0);
                            
                            $stmt = $db->prepare("INSERT INTO project_images (project_id, image_path, caption, sort_order) VALUES (?, ?, ?, ?)");
                            if ($stmt->execute([$project_id, $upload_result['filename'], $caption, $sort_order])) {
                                $uploaded_count++;
                            }
                        }
                    }
                }
                
                if ($uploaded_count > 0) {
                    $success_message = "$uploaded_count images uploaded successfully!";
                } else {
                    $error_message = 'No images were uploaded.';
                }
            }
        } elseif ($action === 'update_image') {
            $image_id = (int)$_POST['image_id'];
            $caption = sanitize_input($_POST['caption']);
            $sort_order = (int)$_POST['sort_order'];
            
            $stmt = $db->prepare("UPDATE project_images SET caption = ?, sort_order = ? WHERE id = ?");
            if ($stmt->execute([$caption, $sort_order, $image_id])) {
                $success_message = 'Image updated successfully!';
            } else {
                $error_message = 'Error updating image.';
            }
        } elseif ($action === 'delete_image') {
            $image_id = (int)$_POST['image_id'];
            
            // Get image path for deletion
            $stmt = $db->prepare("SELECT image_path FROM project_images WHERE id = ?");
            $stmt->execute([$image_id]);
            $image = $stmt->fetch();
            
            if ($image) {
                // Delete from database
                $stmt = $db->prepare("DELETE FROM project_images WHERE id = ?");
                if ($stmt->execute([$image_id])) {
                    // Delete physical file
                    $file_path = UPLOADS_PATH . '/projects/' . $image['image_path'];
                    if (file_exists($file_path)) {
                        unlink($file_path);
                    }
                    $success_message = 'Image deleted successfully!';
                } else {
                    $error_message = 'Error deleting image.';
                }
            }
        }
    }
}

// Get project images if project is selected
$project_images = [];
if ($project_id) {
    $project_images = get_project_images($project_id);
}

// Get all projects for dropdown
$db = getDB();
$stmt = $db->query("SELECT id, title FROM projects WHERE status = 'active' ORDER BY title");
$all_projects = $stmt->fetchAll();

include_admin_header($page_title);
?>

<div class="admin-content">
    <div class="content-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1>Gallery Management</h1>
                <p>Manage project images and galleries</p>
            </div>
            <?php if ($project_id): ?>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                    <i class="fas fa-plus"></i> Add Images
                </button>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <!-- Project Selection -->
    <?php if (!$project_id): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Select Project</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($all_projects as $proj): ?>
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card project-card">
                                <div class="card-body text-center">
                                    <h6><?php echo htmlspecialchars($proj['title']); ?></h6>
                                    <a href="gallery.php?project_id=<?php echo $proj['id']; ?>" class="btn btn-primary">
                                        <i class="fas fa-images"></i> Manage Gallery
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <?php if (empty($all_projects)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-building fa-3x text-muted mb-3"></i>
                        <h5>No Projects Found</h5>
                        <p class="text-muted">Create projects first to manage their galleries.</p>
                        <a href="projects.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Project
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <!-- Project Gallery -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title">
                        Gallery for: <?php echo htmlspecialchars($project['title']); ?>
                    </h5>
                    <a href="gallery.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Projects
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($project_images)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-images fa-3x text-muted mb-3"></i>
                        <h5>No Images Found</h5>
                        <p class="text-muted">Start by uploading images for this project.</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                            <i class="fas fa-plus"></i> Upload First Images
                        </button>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($project_images as $image): ?>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                <div class="gallery-item-card">
                                    <div class="gallery-image">
                                        <img src="<?php echo UPLOADS_URL; ?>/projects/<?php echo $image['image_path']; ?>" 
                                             alt="<?php echo htmlspecialchars($image['caption']); ?>" 
                                             class="img-fluid">
                                        <div class="gallery-overlay">
                                            <div class="gallery-actions">
                                                <button class="btn btn-sm btn-light edit-image" 
                                                        data-image='<?php echo json_encode($image); ?>'>
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger delete-image" 
                                                        data-id="<?php echo $image['id']; ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="gallery-info">
                                        <small class="text-muted">
                                            <?php echo $image['caption'] ?: 'No caption'; ?>
                                        </small>
                                        <br>
                                        <small class="text-muted">Order: <?php echo $image['sort_order']; ?></small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-header">
                    <h5 class="modal-title">Upload Images</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_images">
                    
                    <div id="imageInputs">
                        <div class="image-input-group mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Select Image</label>
                                    <input type="file" class="form-control" name="gallery_images[]" accept="image/*" required>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Caption</label>
                                    <input type="text" class="form-control" name="gallery_captions[]" placeholder="Image caption">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Order</label>
                                    <input type="number" class="form-control" name="sort_orders[]" value="0">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-outline-secondary" id="addImageInput">
                        <i class="fas fa-plus"></i> Add More Images
                    </button>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Upload Images
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Image Modal -->
<div class="modal fade" id="editImageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_image">
                    <input type="hidden" name="image_id" id="editImageId">
                    
                    <div class="mb-3">
                        <img id="editImagePreview" src="" alt="Image preview" class="img-fluid rounded mb-3" style="max-height: 200px;">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Caption</label>
                        <input type="text" class="form-control" name="caption" id="editImageCaption">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Sort Order</label>
                        <input type="number" class="form-control" name="sort_order" id="editImageOrder">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Image
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteImageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this image?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="delete_image">
                    <input type="hidden" name="image_id" id="deleteImageId">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Image
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.project-card {
    transition: transform 0.3s ease;
}

.project-card:hover {
    transform: translateY(-5px);
}

.gallery-item-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.gallery-item-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.gallery-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.gallery-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item-card:hover .gallery-overlay {
    opacity: 1;
}

.gallery-actions .btn {
    margin: 0 5px;
}

.gallery-info {
    padding: 10px;
    background: #f8f9fa;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add more image inputs
    document.getElementById('addImageInput').addEventListener('click', function() {
        const imageInputs = document.getElementById('imageInputs');
        const newInput = document.createElement('div');
        newInput.className = 'image-input-group mb-3';
        newInput.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <input type="file" class="form-control" name="gallery_images[]" accept="image/*" required>
                </div>
                <div class="col-md-4">
                    <input type="text" class="form-control" name="gallery_captions[]" placeholder="Image caption">
                </div>
                <div class="col-md-2">
                    <div class="input-group">
                        <input type="number" class="form-control" name="sort_orders[]" value="0">
                        <button type="button" class="btn btn-outline-danger remove-input">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        imageInputs.appendChild(newInput);
        
        // Add remove functionality
        newInput.querySelector('.remove-input').addEventListener('click', function() {
            newInput.remove();
        });
    });
    
    // Edit image
    document.querySelectorAll('.edit-image').forEach(button => {
        button.addEventListener('click', function() {
            const image = JSON.parse(this.getAttribute('data-image'));
            
            document.getElementById('editImageId').value = image.id;
            document.getElementById('editImageCaption').value = image.caption || '';
            document.getElementById('editImageOrder').value = image.sort_order;
            document.getElementById('editImagePreview').src = '<?php echo UPLOADS_URL; ?>/projects/' + image.image_path;
            
            new bootstrap.Modal(document.getElementById('editImageModal')).show();
        });
    });
    
    // Delete image
    document.querySelectorAll('.delete-image').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            document.getElementById('deleteImageId').value = id;
            new bootstrap.Modal(document.getElementById('deleteImageModal')).show();
        });
    });
});
</script>

<?php include_admin_footer(); ?>
