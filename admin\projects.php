<?php
require_once '../includes/functions.php';
require_admin_login();

$page_title = 'Projects Management';
$success_message = '';
$error_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        if ($action === 'add' || $action === 'edit') {
            $title = sanitize_input($_POST['title']);
            $description = sanitize_input($_POST['description']);
            $short_description = sanitize_input($_POST['short_description']);
            $client = sanitize_input($_POST['client']);
            $location = sanitize_input($_POST['location']);
            $completion_date = $_POST['completion_date'] ?: null;
            $project_value = $_POST['project_value'] ? (float)$_POST['project_value'] : null;
            $category = $_POST['category'] ?? 'completed';
            $status = $_POST['status'] ?? 'active';
            $sort_order = (int)($_POST['sort_order'] ?? 0);
            $slug = create_slug($title);
            
            // Handle featured image upload
            $featured_image = '';
            if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                $upload_result = upload_image($_FILES['featured_image'], 'projects');
                if ($upload_result['success']) {
                    $featured_image = $upload_result['filename'];
                } else {
                    $error_message = $upload_result['message'];
                }
            }
            
            if (empty($error_message)) {
                $db = getDB();
                
                if ($action === 'add') {
                    $stmt = $db->prepare("INSERT INTO projects (title, slug, description, short_description, client, location, completion_date, project_value, category, featured_image, status, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $result = $stmt->execute([$title, $slug, $description, $short_description, $client, $location, $completion_date, $project_value, $category, $featured_image, $status, $sort_order]);
                    
                    if ($result) {
                        $project_id = $db->lastInsertId();
                        $success_message = 'Project added successfully!';
                        
                        // Handle gallery images
                        if (isset($_FILES['gallery_images'])) {
                            foreach ($_FILES['gallery_images']['tmp_name'] as $key => $tmp_name) {
                                if ($_FILES['gallery_images']['error'][$key] === UPLOAD_ERR_OK) {
                                    $gallery_file = [
                                        'tmp_name' => $tmp_name,
                                        'name' => $_FILES['gallery_images']['name'][$key],
                                        'size' => $_FILES['gallery_images']['size'][$key],
                                        'type' => $_FILES['gallery_images']['type'][$key]
                                    ];
                                    
                                    $upload_result = upload_image($gallery_file, 'projects');
                                    if ($upload_result['success']) {
                                        $caption = $_POST['gallery_captions'][$key] ?? '';
                                        $stmt = $db->prepare("INSERT INTO project_images (project_id, image_path, caption, sort_order) VALUES (?, ?, ?, ?)");
                                        $stmt->execute([$project_id, $upload_result['filename'], $caption, $key]);
                                    }
                                }
                            }
                        }
                    } else {
                        $error_message = 'Error adding project.';
                    }
                } else {
                    $id = (int)$_POST['id'];
                    $existing_project = get_project_by_id($id);
                    
                    // Use existing image if no new image uploaded
                    if (empty($featured_image)) {
                        $featured_image = $existing_project['featured_image'];
                    }
                    
                    $stmt = $db->prepare("UPDATE projects SET title = ?, slug = ?, description = ?, short_description = ?, client = ?, location = ?, completion_date = ?, project_value = ?, category = ?, featured_image = ?, status = ?, sort_order = ? WHERE id = ?");
                    $result = $stmt->execute([$title, $slug, $description, $short_description, $client, $location, $completion_date, $project_value, $category, $featured_image, $status, $sort_order, $id]);
                    
                    if ($result) {
                        $success_message = 'Project updated successfully!';
                    } else {
                        $error_message = 'Error updating project.';
                    }
                }
            }
        } elseif ($action === 'delete') {
            $id = (int)$_POST['id'];
            $db = getDB();
            
            // Delete project images first (foreign key constraint)
            $stmt = $db->prepare("DELETE FROM project_images WHERE project_id = ?");
            $stmt->execute([$id]);
            
            // Delete project
            $stmt = $db->prepare("DELETE FROM projects WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            if ($result) {
                $success_message = 'Project deleted successfully!';
            } else {
                $error_message = 'Error deleting project.';
            }
        }
    }
}

// Get all projects
$db = getDB();
$stmt = $db->query("SELECT * FROM projects ORDER BY sort_order ASC, completion_date DESC");
$projects = $stmt->fetchAll();

include_admin_header($page_title);
?>

<div class="admin-content">
    <div class="content-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1>Projects Management</h1>
                <p>Manage your construction projects portfolio</p>
            </div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#projectModal">
                <i class="fas fa-plus"></i> Add New Project
            </button>
        </div>
    </div>
    
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <!-- Projects Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title">All Projects</h5>
        </div>
        <div class="card-body">
            <?php if (empty($projects)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <h5>No Projects Found</h5>
                    <p class="text-muted">Start by adding your first project.</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#projectModal">
                        <i class="fas fa-plus"></i> Add First Project
                    </button>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Project</th>
                                <th>Client</th>
                                <th>Location</th>
                                <th>Category</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($projects as $project): ?>
                                <tr>
                                    <td>
                                        <?php if ($project['featured_image']): ?>
                                            <img src="<?php echo UPLOADS_URL; ?>/<?php echo $project['featured_image']; ?>" 
                                                 alt="<?php echo htmlspecialchars($project['title']); ?>" 
                                                 class="img-thumbnail" style="width: 80px; height: 60px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center" style="width: 80px; height: 60px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($project['title']); ?></strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars(substr($project['short_description'], 0, 50)); ?>...</small>
                                    </td>
                                    <td><?php echo htmlspecialchars($project['client']); ?></td>
                                    <td><?php echo htmlspecialchars($project['location']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $project['category'] === 'completed' ? 'success' : 'warning'; ?>">
                                            <?php echo ucfirst($project['category']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $project['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo ucfirst($project['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo BASE_URL; ?>/project.php?slug=<?php echo $project['slug']; ?>" 
                                               class="btn btn-outline-primary" target="_blank" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-outline-secondary edit-project" 
                                                    data-id="<?php echo $project['id']; ?>" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <a href="gallery.php?project_id=<?php echo $project['id']; ?>" 
                                               class="btn btn-outline-info" title="Gallery">
                                                <i class="fas fa-images"></i>
                                            </a>
                                            <button class="btn btn-outline-danger delete-project" 
                                                    data-id="<?php echo $project['id']; ?>" 
                                                    data-title="<?php echo htmlspecialchars($project['title']); ?>" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Project Modal -->
<div class="modal fade" id="projectModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <form method="POST" enctype="multipart/form-data" id="projectForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="projectModalTitle">Add New Project</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" id="projectAction" value="add">
                    <input type="hidden" name="id" id="projectId">
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">Project Title *</label>
                                <input type="text" class="form-control" name="title" id="projectTitle" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Sort Order</label>
                                <input type="number" class="form-control" name="sort_order" id="projectSortOrder" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Client *</label>
                                <input type="text" class="form-control" name="client" id="projectClient" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Location *</label>
                                <input type="text" class="form-control" name="location" id="projectLocation" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Short Description *</label>
                        <textarea class="form-control" name="short_description" id="projectShortDescription" rows="2" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Full Description</label>
                        <textarea class="form-control" name="description" id="projectDescription" rows="4"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Completion Date</label>
                                <input type="date" class="form-control" name="completion_date" id="projectCompletionDate">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Project Value (£)</label>
                                <input type="number" class="form-control" name="project_value" id="projectValue" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Category</label>
                                <select class="form-control" name="category" id="projectCategory">
                                    <option value="completed">Completed</option>
                                    <option value="ongoing">Ongoing</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">Featured Image *</label>
                                <input type="file" class="form-control" name="featured_image" id="projectFeaturedImage" accept="image/*">
                                <small class="form-text text-muted">Main project image (recommended: 1200x800px)</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select class="form-control" name="status" id="projectStatus">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3" id="gallerySection">
                        <label class="form-label">Gallery Images</label>
                        <div id="galleryInputs">
                            <div class="gallery-input-group mb-2">
                                <div class="row">
                                    <div class="col-md-8">
                                        <input type="file" class="form-control" name="gallery_images[]" accept="image/*">
                                    </div>
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" name="gallery_captions[]" placeholder="Caption (optional)">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="addGalleryInput">
                            <i class="fas fa-plus"></i> Add More Images
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Project
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the project "<span id="deleteProjectTitle"></span>"?</p>
                <p class="text-danger"><small>This will also delete all associated gallery images. This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="deleteProjectId">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Project
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add gallery input
    document.getElementById('addGalleryInput').addEventListener('click', function() {
        const galleryInputs = document.getElementById('galleryInputs');
        const newInput = document.createElement('div');
        newInput.className = 'gallery-input-group mb-2';
        newInput.innerHTML = `
            <div class="row">
                <div class="col-md-8">
                    <input type="file" class="form-control" name="gallery_images[]" accept="image/*">
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" name="gallery_captions[]" placeholder="Caption (optional)">
                        <button type="button" class="btn btn-outline-danger remove-gallery-input">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        galleryInputs.appendChild(newInput);
        
        // Add remove functionality
        newInput.querySelector('.remove-gallery-input').addEventListener('click', function() {
            newInput.remove();
        });
    });
    
    // Edit project
    document.querySelectorAll('.edit-project').forEach(button => {
        button.addEventListener('click', function() {
            const projectId = this.getAttribute('data-id');
            
            // Fetch project data via AJAX or redirect to edit page
            window.location.href = `projects.php?edit=${projectId}`;
        });
    });
    
    // Delete project
    document.querySelectorAll('.delete-project').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const title = this.getAttribute('data-title');
            
            document.getElementById('deleteProjectId').value = id;
            document.getElementById('deleteProjectTitle').textContent = title;
            
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        });
    });
    
    // Reset form when modal is hidden
    document.getElementById('projectModal').addEventListener('hidden.bs.modal', function() {
        document.getElementById('projectForm').reset();
        document.getElementById('projectModalTitle').textContent = 'Add New Project';
        document.getElementById('projectAction').value = 'add';
        document.getElementById('projectId').value = '';
        
        // Reset gallery inputs
        const galleryInputs = document.getElementById('galleryInputs');
        galleryInputs.innerHTML = `
            <div class="gallery-input-group mb-2">
                <div class="row">
                    <div class="col-md-8">
                        <input type="file" class="form-control" name="gallery_images[]" accept="image/*">
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="gallery_captions[]" placeholder="Caption (optional)">
                    </div>
                </div>
            </div>
        `;
    });
});
</script>

<?php include_admin_footer(); ?>
