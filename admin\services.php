<?php
require_once '../includes/functions.php';
require_admin_login();

$page_title = 'Services Management';
$success_message = '';
$error_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        if ($action === 'add' || $action === 'edit') {
            $title = sanitize_input($_POST['title']);
            $description = sanitize_input($_POST['description']);
            $short_description = sanitize_input($_POST['short_description']);
            $slug = create_slug($title);
            $status = $_POST['status'] ?? 'active';
            $sort_order = (int)($_POST['sort_order'] ?? 0);
            
            // Handle image upload
            $image_filename = '';
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $upload_result = upload_image($_FILES['image'], 'services');
                if ($upload_result['success']) {
                    $image_filename = $upload_result['filename'];
                } else {
                    $error_message = $upload_result['message'];
                }
            }
            
            if (empty($error_message)) {
                $db = getDB();
                
                if ($action === 'add') {
                    $stmt = $db->prepare("INSERT INTO services (title, slug, description, short_description, image, status, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $result = $stmt->execute([$title, $slug, $description, $short_description, $image_filename, $status, $sort_order]);
                    
                    if ($result) {
                        $success_message = 'Service added successfully!';
                    } else {
                        $error_message = 'Error adding service.';
                    }
                } else {
                    $id = (int)$_POST['id'];
                    $existing_service = get_service_by_id($id);
                    
                    // Use existing image if no new image uploaded
                    if (empty($image_filename)) {
                        $image_filename = $existing_service['image'];
                    }
                    
                    $stmt = $db->prepare("UPDATE services SET title = ?, slug = ?, description = ?, short_description = ?, image = ?, status = ?, sort_order = ? WHERE id = ?");
                    $result = $stmt->execute([$title, $slug, $description, $short_description, $image_filename, $status, $sort_order, $id]);
                    
                    if ($result) {
                        $success_message = 'Service updated successfully!';
                    } else {
                        $error_message = 'Error updating service.';
                    }
                }
            }
        } elseif ($action === 'delete') {
            $id = (int)$_POST['id'];
            $db = getDB();
            $stmt = $db->prepare("DELETE FROM services WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            if ($result) {
                $success_message = 'Service deleted successfully!';
            } else {
                $error_message = 'Error deleting service.';
            }
        }
    }
}

// Get all services
$db = getDB();
$stmt = $db->query("SELECT * FROM services ORDER BY sort_order ASC, title ASC");
$services = $stmt->fetchAll();

// Get service for editing
$edit_service = null;
if (isset($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    $edit_service = get_service_by_id($edit_id);
}

include_admin_header($page_title);
?>

<div class="admin-content">
    <div class="content-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1>Services Management</h1>
                <p>Manage your construction services</p>
            </div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#serviceModal">
                <i class="fas fa-plus"></i> Add New Service
            </button>
        </div>
    </div>
    
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <!-- Services Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title">All Services</h5>
        </div>
        <div class="card-body">
            <?php if (empty($services)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                    <h5>No Services Found</h5>
                    <p class="text-muted">Start by adding your first service.</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#serviceModal">
                        <i class="fas fa-plus"></i> Add First Service
                    </button>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Title</th>
                                <th>Short Description</th>
                                <th>Status</th>
                                <th>Sort Order</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($services as $service): ?>
                                <tr>
                                    <td>
                                        <?php if ($service['image']): ?>
                                            <img src="<?php echo UPLOADS_URL; ?>/<?php echo $service['image']; ?>" 
                                                 alt="<?php echo htmlspecialchars($service['title']); ?>" 
                                                 class="img-thumbnail" style="width: 60px; height: 45px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center" style="width: 60px; height: 45px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($service['title']); ?></strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($service['slug']); ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars(substr($service['short_description'], 0, 100)); ?>...</td>
                                    <td>
                                        <span class="badge bg-<?php echo $service['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo ucfirst($service['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo $service['sort_order']; ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo BASE_URL; ?>/service.php?slug=<?php echo $service['slug']; ?>" 
                                               class="btn btn-outline-primary" target="_blank" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-outline-secondary edit-service" 
                                                    data-service='<?php echo json_encode($service); ?>' title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger delete-service" 
                                                    data-id="<?php echo $service['id']; ?>" 
                                                    data-title="<?php echo htmlspecialchars($service['title']); ?>" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Service Modal -->
<div class="modal fade" id="serviceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" enctype="multipart/form-data" id="serviceForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="serviceModalTitle">Add New Service</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" id="serviceAction" value="add">
                    <input type="hidden" name="id" id="serviceId">
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">Service Title *</label>
                                <input type="text" class="form-control" name="title" id="serviceTitle" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Sort Order</label>
                                <input type="number" class="form-control" name="sort_order" id="serviceSortOrder" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Short Description *</label>
                        <textarea class="form-control" name="short_description" id="serviceShortDescription" rows="2" required></textarea>
                        <small class="form-text text-muted">Brief description for service cards and listings</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Full Description</label>
                        <textarea class="form-control" name="description" id="serviceDescription" rows="5"></textarea>
                        <small class="form-text text-muted">Detailed description for the service page</small>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">Service Image</label>
                                <input type="file" class="form-control" name="image" id="serviceImage" accept="image/*">
                                <small class="form-text text-muted">Recommended size: 800x600px</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select class="form-control" name="status" id="serviceStatus">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div id="currentImagePreview" style="display: none;">
                        <label class="form-label">Current Image</label>
                        <div>
                            <img id="currentImage" src="" alt="Current service image" class="img-thumbnail" style="max-width: 200px;">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Service
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the service "<span id="deleteServiceTitle"></span>"?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="deleteServiceId">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Service
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Edit service
    document.querySelectorAll('.edit-service').forEach(button => {
        button.addEventListener('click', function() {
            const service = JSON.parse(this.getAttribute('data-service'));
            
            document.getElementById('serviceModalTitle').textContent = 'Edit Service';
            document.getElementById('serviceAction').value = 'edit';
            document.getElementById('serviceId').value = service.id;
            document.getElementById('serviceTitle').value = service.title;
            document.getElementById('serviceShortDescription').value = service.short_description;
            document.getElementById('serviceDescription').value = service.description || '';
            document.getElementById('serviceStatus').value = service.status;
            document.getElementById('serviceSortOrder').value = service.sort_order;
            
            // Show current image if exists
            if (service.image) {
                document.getElementById('currentImagePreview').style.display = 'block';
                document.getElementById('currentImage').src = '<?php echo UPLOADS_URL; ?>/' + service.image;
            } else {
                document.getElementById('currentImagePreview').style.display = 'none';
            }
            
            new bootstrap.Modal(document.getElementById('serviceModal')).show();
        });
    });
    
    // Delete service
    document.querySelectorAll('.delete-service').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const title = this.getAttribute('data-title');
            
            document.getElementById('deleteServiceId').value = id;
            document.getElementById('deleteServiceTitle').textContent = title;
            
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        });
    });
    
    // Reset form when modal is hidden
    document.getElementById('serviceModal').addEventListener('hidden.bs.modal', function() {
        document.getElementById('serviceForm').reset();
        document.getElementById('serviceModalTitle').textContent = 'Add New Service';
        document.getElementById('serviceAction').value = 'add';
        document.getElementById('serviceId').value = '';
        document.getElementById('currentImagePreview').style.display = 'none';
    });
});
</script>

<?php include_admin_footer(); ?>
