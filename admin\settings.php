<?php
require_once '../includes/functions.php';
require_admin_login();

$page_title = 'Settings';
$success_message = '';
$error_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        if ($action === 'update_settings') {
            $updates = 0;
            
            // Company Information
            $company_settings = [
                'company_name' => 'Flori Construction Ltd',
                'company_address' => '',
                'company_phone' => '',
                'company_mobile' => '',
                'company_email' => '',
                'company_website' => '',
                'company_description' => ''
            ];
            
            foreach ($company_settings as $key => $default) {
                if (isset($_POST[$key])) {
                    $value = sanitize_input($_POST[$key]);
                    if (update_setting($key, $value, 'text', ucwords(str_replace('_', ' ', $key)))) {
                        $updates++;
                    }
                }
            }
            
            // Social Media
            $social_settings = [
                'facebook_url' => '',
                'instagram_url' => '',
                'linkedin_url' => '',
                'youtube_url' => '',
                'twitter_url' => ''
            ];
            
            foreach ($social_settings as $key => $default) {
                if (isset($_POST[$key])) {
                    $value = sanitize_input($_POST[$key]);
                    if (update_setting($key, $value, 'url', ucwords(str_replace('_', ' ', $key)))) {
                        $updates++;
                    }
                }
            }
            
            // SEO Settings
            $seo_settings = [
                'site_title' => 'Flori Construction Ltd',
                'site_description' => '',
                'site_keywords' => '',
                'google_analytics_id' => '',
                'google_maps_api_key' => ''
            ];
            
            foreach ($seo_settings as $key => $default) {
                if (isset($_POST[$key])) {
                    $value = sanitize_input($_POST[$key]);
                    if (update_setting($key, $value, 'text', ucwords(str_replace('_', ' ', $key)))) {
                        $updates++;
                    }
                }
            }
            
            // Email Settings
            $email_settings = [
                'smtp_host' => '',
                'smtp_port' => '587',
                'smtp_username' => '',
                'smtp_password' => '',
                'smtp_encryption' => 'tls',
                'admin_email' => '',
                'contact_email' => ''
            ];
            
            foreach ($email_settings as $key => $default) {
                if (isset($_POST[$key])) {
                    $value = sanitize_input($_POST[$key]);
                    if (update_setting($key, $value, 'text', ucwords(str_replace('_', ' ', $key)))) {
                        $updates++;
                    }
                }
            }
            
            if ($updates > 0) {
                $success_message = "Successfully updated $updates settings!";
            } else {
                $error_message = 'No settings were updated.';
            }
        } elseif ($action === 'change_password') {
            $current_password = $_POST['current_password'];
            $new_password = $_POST['new_password'];
            $confirm_password = $_POST['confirm_password'];
            
            if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                $error_message = 'Please fill in all password fields.';
            } elseif ($new_password !== $confirm_password) {
                $error_message = 'New passwords do not match.';
            } elseif (strlen($new_password) < 6) {
                $error_message = 'New password must be at least 6 characters long.';
            } else {
                // Verify current password
                $db = getDB();
                $stmt = $db->prepare("SELECT password FROM users WHERE id = ?");
                $stmt->execute([$_SESSION['admin_id']]);
                $user = $stmt->fetch();
                
                if (password_verify($current_password, $user['password'])) {
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $db->prepare("UPDATE users SET password = ? WHERE id = ?");
                    if ($stmt->execute([$hashed_password, $_SESSION['admin_id']])) {
                        $success_message = 'Password changed successfully!';
                    } else {
                        $error_message = 'Error updating password.';
                    }
                } else {
                    $error_message = 'Current password is incorrect.';
                }
            }
        }
    }
}

include_admin_header($page_title);
?>

<div class="admin-content">
    <div class="content-header">
        <h1>Settings</h1>
        <p>Configure website settings and preferences</p>
    </div>
    
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <!-- Settings Tabs -->
    <div class="card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="company-tab" data-bs-toggle="tab" data-bs-target="#company" type="button" role="tab">
                        <i class="fas fa-building"></i> Company Info
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="social-tab" data-bs-toggle="tab" data-bs-target="#social" type="button" role="tab">
                        <i class="fas fa-share-alt"></i> Social Media
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="seo-tab" data-bs-toggle="tab" data-bs-target="#seo" type="button" role="tab">
                        <i class="fas fa-search"></i> SEO
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                        <i class="fas fa-envelope"></i> Email
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="account-tab" data-bs-toggle="tab" data-bs-target="#account" type="button" role="tab">
                        <i class="fas fa-user"></i> Account
                    </button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="settingsTabContent">
                <!-- Company Information -->
                <div class="tab-pane fade show active" id="company" role="tabpanel">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_settings">
                        
                        <h5 class="mb-3">Company Information</h5>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Company Name</label>
                                <input type="text" class="form-control" name="company_name" 
                                       value="<?php echo htmlspecialchars(get_setting('company_name', 'Flori Construction Ltd')); ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Website URL</label>
                                <input type="url" class="form-control" name="company_website" 
                                       value="<?php echo htmlspecialchars(get_setting('company_website')); ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Company Address</label>
                            <textarea class="form-control" name="company_address" rows="2"><?php echo htmlspecialchars(get_setting('company_address')); ?></textarea>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" name="company_phone" 
                                       value="<?php echo htmlspecialchars(get_setting('company_phone')); ?>">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Mobile Number</label>
                                <input type="tel" class="form-control" name="company_mobile" 
                                       value="<?php echo htmlspecialchars(get_setting('company_mobile')); ?>">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Email Address</label>
                                <input type="email" class="form-control" name="company_email" 
                                       value="<?php echo htmlspecialchars(get_setting('company_email')); ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Company Description</label>
                            <textarea class="form-control" name="company_description" rows="3"><?php echo htmlspecialchars(get_setting('company_description')); ?></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Company Settings
                        </button>
                    </form>
                </div>
                
                <!-- Social Media -->
                <div class="tab-pane fade" id="social" role="tabpanel">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_settings">
                        
                        <h5 class="mb-3">Social Media Links</h5>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Facebook URL</label>
                                <input type="url" class="form-control" name="facebook_url" 
                                       value="<?php echo htmlspecialchars(get_setting('facebook_url')); ?>"
                                       placeholder="https://facebook.com/yourpage">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Instagram URL</label>
                                <input type="url" class="form-control" name="instagram_url" 
                                       value="<?php echo htmlspecialchars(get_setting('instagram_url')); ?>"
                                       placeholder="https://instagram.com/yourprofile">
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">LinkedIn URL</label>
                                <input type="url" class="form-control" name="linkedin_url" 
                                       value="<?php echo htmlspecialchars(get_setting('linkedin_url')); ?>"
                                       placeholder="https://linkedin.com/company/yourcompany">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">YouTube URL</label>
                                <input type="url" class="form-control" name="youtube_url" 
                                       value="<?php echo htmlspecialchars(get_setting('youtube_url')); ?>"
                                       placeholder="https://youtube.com/yourchannel">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Twitter URL</label>
                            <input type="url" class="form-control" name="twitter_url" 
                                   value="<?php echo htmlspecialchars(get_setting('twitter_url')); ?>"
                                   placeholder="https://twitter.com/yourhandle">
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Social Media Settings
                        </button>
                    </form>
                </div>
                
                <!-- SEO Settings -->
                <div class="tab-pane fade" id="seo" role="tabpanel">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_settings">
                        
                        <h5 class="mb-3">SEO & Analytics</h5>
                        
                        <div class="mb-3">
                            <label class="form-label">Site Title</label>
                            <input type="text" class="form-control" name="site_title" 
                                   value="<?php echo htmlspecialchars(get_setting('site_title', 'Flori Construction Ltd')); ?>">
                            <small class="form-text text-muted">Default title for your website</small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Site Description</label>
                            <textarea class="form-control" name="site_description" rows="2"><?php echo htmlspecialchars(get_setting('site_description')); ?></textarea>
                            <small class="form-text text-muted">Default meta description for your website</small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Keywords</label>
                            <input type="text" class="form-control" name="site_keywords" 
                                   value="<?php echo htmlspecialchars(get_setting('site_keywords')); ?>"
                                   placeholder="construction, building, london, civil engineering">
                            <small class="form-text text-muted">Comma-separated keywords</small>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Google Analytics ID</label>
                                <input type="text" class="form-control" name="google_analytics_id" 
                                       value="<?php echo htmlspecialchars(get_setting('google_analytics_id')); ?>"
                                       placeholder="G-XXXXXXXXXX">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Google Maps API Key</label>
                                <input type="text" class="form-control" name="google_maps_api_key" 
                                       value="<?php echo htmlspecialchars(get_setting('google_maps_api_key')); ?>">
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save SEO Settings
                        </button>
                    </form>
                </div>
                
                <!-- Email Settings -->
                <div class="tab-pane fade" id="email" role="tabpanel">
                    <form method="POST">
                        <input type="hidden" name="action" value="update_settings">
                        
                        <h5 class="mb-3">Email Configuration</h5>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Admin Email</label>
                                <input type="email" class="form-control" name="admin_email" 
                                       value="<?php echo htmlspecialchars(get_setting('admin_email')); ?>">
                                <small class="form-text text-muted">Email for admin notifications</small>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Contact Form Email</label>
                                <input type="email" class="form-control" name="contact_email" 
                                       value="<?php echo htmlspecialchars(get_setting('contact_email')); ?>">
                                <small class="form-text text-muted">Email to receive contact form submissions</small>
                            </div>
                        </div>
                        
                        <h6 class="text-muted mb-3">SMTP Settings (Optional)</h6>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">SMTP Host</label>
                                <input type="text" class="form-control" name="smtp_host" 
                                       value="<?php echo htmlspecialchars(get_setting('smtp_host')); ?>"
                                       placeholder="smtp.gmail.com">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">SMTP Port</label>
                                <input type="number" class="form-control" name="smtp_port" 
                                       value="<?php echo htmlspecialchars(get_setting('smtp_port', '587')); ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Encryption</label>
                                <select class="form-control" name="smtp_encryption">
                                    <option value="tls" <?php echo get_setting('smtp_encryption') === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                    <option value="ssl" <?php echo get_setting('smtp_encryption') === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                    <option value="none" <?php echo get_setting('smtp_encryption') === 'none' ? 'selected' : ''; ?>>None</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">SMTP Username</label>
                                <input type="text" class="form-control" name="smtp_username" 
                                       value="<?php echo htmlspecialchars(get_setting('smtp_username')); ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">SMTP Password</label>
                                <input type="password" class="form-control" name="smtp_password" 
                                       value="<?php echo htmlspecialchars(get_setting('smtp_password')); ?>">
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Email Settings
                        </button>
                    </form>
                </div>
                
                <!-- Account Settings -->
                <div class="tab-pane fade" id="account" role="tabpanel">
                    <h5 class="mb-3">Account Information</h5>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6>Current User</h6>
                                    <p><strong>Username:</strong> <?php echo htmlspecialchars($_SESSION['admin_username']); ?></p>
                                    <p><strong>Email:</strong> <?php echo htmlspecialchars($_SESSION['admin_email']); ?></p>
                                    <p><strong>Role:</strong> <?php echo ucfirst($_SESSION['admin_role']); ?></p>
                                    <p><strong>Last Login:</strong> <?php echo date('M j, Y g:i A', $_SESSION['login_time']); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <h6 class="mb-3">Change Password</h6>
                    <form method="POST">
                        <input type="hidden" name="action" value="change_password">
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">Current Password</label>
                                <input type="password" class="form-control" name="current_password" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">New Password</label>
                                <input type="password" class="form-control" name="new_password" required minlength="6">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" name="confirm_password" required minlength="6">
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-key"></i> Change Password
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('input[required], textarea[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields.');
            }
        });
    });
    
    // Password confirmation validation
    const passwordForm = document.querySelector('form[action*="change_password"]');
    if (passwordForm) {
        const newPassword = passwordForm.querySelector('input[name="new_password"]');
        const confirmPassword = passwordForm.querySelector('input[name="confirm_password"]');
        
        function validatePasswords() {
            if (newPassword.value && confirmPassword.value) {
                if (newPassword.value !== confirmPassword.value) {
                    confirmPassword.setCustomValidity('Passwords do not match');
                } else {
                    confirmPassword.setCustomValidity('');
                }
            }
        }
        
        newPassword.addEventListener('input', validatePasswords);
        confirmPassword.addEventListener('input', validatePasswords);
    }
});
</script>

<?php include_admin_footer(); ?>
