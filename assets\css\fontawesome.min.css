/* FontAwesome Icons - Basic Implementation */
/* For full FontAwesome, download from https://fontawesome.com/ */

.fas, .far, .fab {
    font-family: "Font Awesome 5 Free", "Font Awesome 5 Brands";
    font-weight: 900;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
}

.fab {
    font-family: "Font Awesome 5 Brands";
    font-weight: 400;
}

/* Common Icons */
.fa-phone:before { content: "📞"; }
.fa-envelope:before { content: "✉️"; }
.fa-map-marker-alt:before { content: "📍"; }
.fa-mobile-alt:before { content: "📱"; }
.fa-facebook-f:before { content: "f"; }
.fa-instagram:before { content: "📷"; }
.fa-linkedin-in:before { content: "💼"; }
.fa-youtube:before { content: "📺"; }
.fa-twitter:before { content: "🐦"; }
.fa-arrow-right:before { content: "→"; }
.fa-chevron-down:before { content: "⌄"; }
.fa-chevron-up:before { content: "⌃"; }
.fa-bars:before { content: "☰"; }
.fa-times:before { content: "✕"; }
.fa-check-circle:before { content: "✓"; }
.fa-award:before { content: "🏆"; }
.fa-users:before { content: "👥"; }
.fa-tools:before { content: "🔧"; }
.fa-building:before { content: "🏢"; }
.fa-hard-hat:before { content: "⛑️"; }
.fa-drafting-compass:before { content: "📐"; }
.fa-comments:before { content: "💬"; }
.fa-key:before { content: "🔑"; }
.fa-clock:before { content: "🕐"; }
.fa-shield-alt:before { content: "🛡️"; }
.fa-lightbulb:before { content: "💡"; }
.fa-handshake:before { content: "🤝"; }
.fa-leaf:before { content: "🍃"; }
.fa-eye:before { content: "👁️"; }
.fa-bullseye:before { content: "🎯"; }
.fa-search-plus:before { content: "🔍"; }
.fa-images:before { content: "🖼️"; }
.fa-external-link-alt:before { content: "🔗"; }
.fa-sign-out-alt:before { content: "🚪"; }
.fa-tachometer-alt:before { content: "📊"; }
.fa-edit:before { content: "✏️"; }
.fa-cog:before { content: "⚙️"; }
.fa-user:before { content: "👤"; }
.fa-user-circle:before { content: "👤"; }
.fa-plus:before { content: "+"; }
.fa-paper-plane:before { content: "✈️"; }
.fa-exclamation-circle:before { content: "⚠️"; }
.fa-check:before { content: "✓"; }
.fa-list-ul:before { content: "•"; }
.fa-list-ol:before { content: "1."; }
.fa-bold:before { content: "B"; }
.fa-italic:before { content: "I"; }
.fa-underline:before { content: "U"; }
.fa-calendar:before { content: "📅"; }

/* Utility classes */
.fa-fw {
    width: 1.25em;
    text-align: center;
}

.fa-lg {
    font-size: 1.33333em;
    line-height: 0.75em;
    vertical-align: -0.0667em;
}

.fa-2x {
    font-size: 2em;
}

.fa-3x {
    font-size: 3em;
}

/* Note: This is a basic implementation using Unicode characters.
   For full FontAwesome functionality, download the complete library from:
   https://fontawesome.com/download
   
   Or use CDN:
   <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
*/
