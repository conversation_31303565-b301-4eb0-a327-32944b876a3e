/* Bootstrap 5.3.0 Bundle - Basic Implementation */
/* For full Bootstrap functionality, download from https://getbootstrap.com/ */

// Basic Bootstrap-like functionality
(function() {
    'use strict';

    // Modal functionality
    class Modal {
        constructor(element) {
            this.element = element;
            this.backdrop = null;
        }

        show() {
            this.element.style.display = 'block';
            this.element.classList.add('show');
            document.body.classList.add('modal-open');
            this.createBackdrop();
        }

        hide() {
            this.element.style.display = 'none';
            this.element.classList.remove('show');
            document.body.classList.remove('modal-open');
            this.removeBackdrop();
        }

        createBackdrop() {
            this.backdrop = document.createElement('div');
            this.backdrop.className = 'modal-backdrop fade show';
            document.body.appendChild(this.backdrop);
        }

        removeBackdrop() {
            if (this.backdrop) {
                this.backdrop.remove();
                this.backdrop = null;
            }
        }

        static getInstance(element) {
            return new Modal(element);
        }
    }

    // Collapse functionality
    class Collapse {
        constructor(element) {
            this.element = element;
        }

        toggle() {
            if (this.element.classList.contains('show')) {
                this.hide();
            } else {
                this.show();
            }
        }

        show() {
            this.element.classList.add('show');
            this.element.style.height = this.element.scrollHeight + 'px';
        }

        hide() {
            this.element.classList.remove('show');
            this.element.style.height = '0px';
        }
    }

    // Dropdown functionality
    class Dropdown {
        constructor(element) {
            this.element = element;
            this.menu = element.nextElementSibling;
        }

        toggle() {
            if (this.menu.classList.contains('show')) {
                this.hide();
            } else {
                this.show();
            }
        }

        show() {
            this.menu.classList.add('show');
        }

        hide() {
            this.menu.classList.remove('show');
        }
    }

    // Alert functionality
    class Alert {
        constructor(element) {
            this.element = element;
        }

        close() {
            this.element.remove();
        }
    }

    // Initialize Bootstrap components
    document.addEventListener('DOMContentLoaded', function() {
        // Modal triggers
        document.querySelectorAll('[data-bs-toggle="modal"]').forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('data-bs-target'));
                if (target) {
                    const modal = new Modal(target);
                    modal.show();
                }
            });
        });

        // Modal close buttons
        document.querySelectorAll('[data-bs-dismiss="modal"]').forEach(closeBtn => {
            closeBtn.addEventListener('click', function() {
                const modal = this.closest('.modal');
                if (modal) {
                    const modalInstance = new Modal(modal);
                    modalInstance.hide();
                }
            });
        });

        // Collapse triggers
        document.querySelectorAll('[data-bs-toggle="collapse"]').forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('data-bs-target'));
                if (target) {
                    const collapse = new Collapse(target);
                    collapse.toggle();
                }
            });
        });

        // Dropdown triggers
        document.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                e.preventDefault();
                const dropdown = new Dropdown(this);
                dropdown.toggle();
            });
        });

        // Alert close buttons
        document.querySelectorAll('[data-bs-dismiss="alert"]').forEach(closeBtn => {
            closeBtn.addEventListener('click', function() {
                const alert = this.closest('.alert');
                if (alert) {
                    const alertInstance = new Alert(alert);
                    alertInstance.close();
                }
            });
        });

        // Navbar toggler
        document.querySelectorAll('.navbar-toggler').forEach(toggler => {
            toggler.addEventListener('click', function() {
                const target = document.querySelector(this.getAttribute('data-bs-target'));
                if (target) {
                    const collapse = new Collapse(target);
                    collapse.toggle();
                }
            });
        });
    });

    // Global Bootstrap object
    window.bootstrap = {
        Modal: Modal,
        Collapse: Collapse,
        Dropdown: Dropdown,
        Alert: Alert
    };

})();

/* Note: This is a basic implementation of Bootstrap JavaScript functionality.
   For full Bootstrap features, download the complete library from:
   https://getbootstrap.com/docs/5.3/getting-started/download/
   
   Or use CDN:
   <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
*/
