<?php
/**
 * Placeholder Image Generator
 * Creates basic placeholder images for the website
 */

// Create placeholder images using GD library
function createPlaceholder($width, $height, $text, $filename) {
    // Create image
    $image = imagecreate($width, $height);
    
    // Colors
    $bg_color = imagecolorallocate($image, 240, 240, 240);
    $text_color = imagecolorallocate($image, 100, 100, 100);
    $border_color = imagecolorallocate($image, 200, 200, 200);
    
    // Fill background
    imagefill($image, 0, 0, $bg_color);
    
    // Add border
    imagerectangle($image, 0, 0, $width-1, $height-1, $border_color);
    
    // Add text
    $font_size = 5;
    $text_width = imagefontwidth($font_size) * strlen($text);
    $text_height = imagefontheight($font_size);
    $x = ($width - $text_width) / 2;
    $y = ($height - $text_height) / 2;
    
    imagestring($image, $font_size, $x, $y, $text, $text_color);
    
    // Add dimensions text
    $dim_text = $width . 'x' . $height;
    $dim_width = imagefontwidth(3) * strlen($dim_text);
    $dim_x = ($width - $dim_width) / 2;
    $dim_y = $y + 20;
    
    imagestring($image, 3, $dim_x, $dim_y, $dim_text, $text_color);
    
    // Save image
    imagejpeg($image, $filename, 80);
    imagedestroy($image);
    
    echo "Created: $filename\n";
}

// Create directories if they don't exist
$directories = [
    'assets/images',
    'uploads/services',
    'uploads/projects',
    'uploads/gallery'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Create placeholder images
echo "Creating placeholder images...\n\n";

// Logo placeholders
createPlaceholder(200, 60, 'FLORI CONSTRUCTION', 'assets/images/logo.png');
createPlaceholder(200, 60, 'FLORI CONSTRUCTION', 'assets/images/logo-white.png');

// Hero and main images
createPlaceholder(1920, 1080, 'Hero Background', 'assets/images/hero-bg.jpg');
createPlaceholder(800, 600, 'About Main Image', 'assets/images/about-main.jpg');
createPlaceholder(400, 300, 'About Small Image', 'assets/images/about-small.jpg');
createPlaceholder(800, 600, 'About Detail', 'assets/images/about-detail.jpg');
createPlaceholder(800, 600, 'Why Choose Us', 'assets/images/why-choose-us.jpg');

// Team images
createPlaceholder(400, 400, 'Team Member 1', 'assets/images/team-1.jpg');
createPlaceholder(400, 400, 'Team Member 2', 'assets/images/team-2.jpg');
createPlaceholder(400, 400, 'Team Member 3', 'assets/images/team-3.jpg');

// Service images
$services = [
    'civil-engineering' => 'Civil Engineering',
    'groundworks' => 'Groundworks',
    'rc-frames' => 'RC Frames',
    'basements' => 'Basements',
    'hard-landscaping' => 'Hard Landscaping'
];

foreach ($services as $slug => $name) {
    createPlaceholder(800, 600, $name, "uploads/services/{$slug}.jpg");
}

// Project images
$projects = [
    'becanham-main' => 'Becanham Development',
    'walker-school-main' => 'Walker Primary School',
    'alton-sports-main' => 'Alton Sports Centre',
    'clapham-main' => 'Clapham South Development',
    'addenbrookes-main' => 'Addenbrookes Hospital'
];

foreach ($projects as $filename => $name) {
    createPlaceholder(1200, 800, $name, "uploads/projects/{$filename}.jpg");
}

// Create favicon
createPlaceholder(32, 32, 'FC', 'assets/images/favicon.ico');

echo "\nPlaceholder images created successfully!\n";
echo "Note: Replace these with actual images for production use.\n";
?>
