-- Flori Construction Ltd Database Schema
-- Created for the construction company website

CREATE DATABASE IF NOT EXISTS flori_construction;
USE flori_construction;

-- Users table for admin authentication
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'editor') DEFAULT 'admin',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Services table
CREATE TABLE services (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    short_description VARCHAR(255),
    image VARCHAR(255),
    icon VARCHAR(255),
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Projects table
CREATE TABLE projects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    short_description VARCHAR(255),
    client VARCHAR(100),
    location VARCHAR(100),
    completion_date DATE,
    project_value DECIMAL(15,2),
    category ENUM('completed', 'ongoing') DEFAULT 'completed',
    featured_image VARCHAR(255),
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Project images table for galleries
CREATE TABLE project_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    caption VARCHAR(255),
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- Content management table for dynamic content
CREATE TABLE content (
    id INT PRIMARY KEY AUTO_INCREMENT,
    page VARCHAR(50) NOT NULL,
    section VARCHAR(50) NOT NULL,
    content_key VARCHAR(100) NOT NULL,
    content_value TEXT,
    content_type ENUM('text', 'html', 'image', 'url') DEFAULT 'text',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_content (page, section, content_key)
);

-- Contact submissions table
CREATE TABLE contact_submissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    subject VARCHAR(200),
    message TEXT NOT NULL,
    status ENUM('new', 'read', 'replied') DEFAULT 'new',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Settings table for general website settings
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- Insert sample services
INSERT INTO services (title, slug, description, short_description, image, icon, sort_order) VALUES
('Civil Engineering', 'civil-engineering', 'Comprehensive civil engineering solutions for infrastructure development, including roads, bridges, and public works projects.', 'Infrastructure development and public works solutions', 'civil-engineering.jpg', 'icon-civil.png', 1),
('Groundworks', 'groundworks', 'Professional groundworks services including excavation, foundations, drainage, and site preparation for construction projects.', 'Excavation, foundations, and site preparation services', 'groundworks.jpg', 'icon-groundworks.png', 2),
('RC Frames', 'rc-frames', 'Reinforced concrete frame construction for residential, commercial, and industrial buildings with precision and quality.', 'Reinforced concrete frame construction services', 'rc-frames.jpg', 'icon-rc-frames.png', 3),
('Basements', 'basements', 'Specialist basement construction and waterproofing services for residential and commercial properties.', 'Basement construction and waterproofing solutions', 'basements.jpg', 'icon-basements.png', 4),
('Hard Landscaping', 'hard-landscaping', 'Professional hard landscaping services including paving, retaining walls, and outdoor construction features.', 'Paving, retaining walls, and outdoor construction', 'hard-landscaping.jpg', 'icon-landscaping.png', 5);

-- Insert sample projects
INSERT INTO projects (title, slug, description, short_description, client, location, completion_date, category, featured_image, sort_order) VALUES
('Becanham Residential Development', 'becanham-residential', 'Complete residential development project including groundworks, RC frame construction, and finishing works.', 'Modern residential development with high-quality finishes', 'Becanham Development Ltd', 'Becanham, London', '2023-08-15', 'completed', 'becanham-main.jpg', 1),
('Walker Primary School Extension', 'walker-primary-school', 'School extension project including new classrooms, sports facilities, and playground areas.', 'Educational facility expansion with modern amenities', 'Walker Education Trust', 'Walker, London', '2023-06-30', 'completed', 'walker-school-main.jpg', 2),
('Alton Sports Centre', 'alton-sports-centre', 'Complete sports centre construction with swimming pool, gymnasium, and recreational facilities.', 'State-of-the-art sports and recreational facility', 'Alton Council', 'Alton, Hampshire', '2023-09-20', 'completed', 'alton-sports-main.jpg', 3),
('Clapham South Development', 'clapham-south-thurleigh', 'Luxury residential development on Thurleigh Road with basement construction and landscaping.', 'Premium residential development with basement levels', 'Private Developer', 'Clapham South, London', '2023-07-10', 'completed', 'clapham-main.jpg', 4),
('Addenbrookes Hospital Extension', 'addenbrookes-hospital', 'Hospital extension project including specialized medical facilities and infrastructure.', 'Medical facility expansion with specialized requirements', 'NHS Foundation Trust', 'Cambridge', '2023-05-25', 'completed', 'addenbrookes-main.jpg', 5);

-- Insert sample content for homepage
INSERT INTO content (page, section, content_key, content_value, content_type) VALUES
('home', 'hero', 'title', 'We design the places where people love to be together.', 'text'),
('home', 'hero', 'subtitle', 'Creating exceptional spaces through innovative construction and design solutions', 'text'),
('home', 'hero', 'background_image', 'hero-bg.jpg', 'image'),
('home', 'about', 'title', 'About our company', 'text'),
('home', 'about', 'description', 'Flori Construction Ltd began its journey in the heart of London, founded on the principles of quality, professionalism, and an unwavering dedication to customer satisfaction.', 'text'),
('home', 'services', 'title', 'Services We Provide', 'text'),
('home', 'services', 'subtitle', 'We are committed to delivering high-quality services in these areas and more.', 'text'),
('home', 'projects', 'title', 'Every Project is Unique and Custom Made', 'text'),
('home', 'projects', 'subtitle', 'We are proud to showcase a selection of our completed projects that demonstrate our expertise.', 'text');

-- Insert company settings
INSERT INTO settings (setting_key, setting_value, setting_type, description) VALUES
('company_name', 'Flori Construction Ltd', 'text', 'Company name'),
('company_phone', '0208 914 7883', 'text', 'Primary phone number'),
('company_mobile', '078 8292 3621', 'text', 'Mobile phone number'),
('company_email', '<EMAIL>', 'text', 'Primary email address'),
('company_address', '662 High Road North Finchley, London N12 0NL', 'text', 'Company address'),
('facebook_url', 'https://www.facebook.com/FloriConstructionLtd', 'text', 'Facebook page URL'),
('instagram_url', 'https://www.instagram.com/flori_construction_ltd/', 'text', 'Instagram page URL'),
('linkedin_url', 'https://www.linkedin.com/in/floriconstructionltd/', 'text', 'LinkedIn page URL'),
('youtube_url', 'https://www.youtube.com/@floriconstructionltd7045', 'text', 'YouTube channel URL');
