<?php
require_once 'includes/functions.php';

$page_title = 'Gallery';
$description = 'View our construction project gallery. See examples of our quality work in civil engineering, groundworks, RC frames, basements, and hard landscaping.';

include_header($page_title, $description);

// Get all projects with images
$projects = get_projects(null, 'active');
?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="page-header-content">
                    <h1 class="page-title">Gallery</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Gallery</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Gallery Section -->
<section class="gallery-section py-5">
    <div class="container">
        <div class="section-header text-center mb-5">
            <span class="section-subtitle">Our Work</span>
            <h2 class="section-title">Project Gallery</h2>
            <p class="section-description">
                Explore our portfolio of completed construction projects showcasing our expertise and quality craftsmanship.
            </p>
        </div>
        
        <?php if (empty($projects)): ?>
            <div class="row">
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-images fa-3x text-muted mb-3"></i>
                        <h4>No Images Available</h4>
                        <p class="text-muted">Gallery images will be displayed here once projects are added.</p>
                        <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-primary">Contact Us</a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Gallery Grid -->
            <div class="row gallery-grid">
                <?php foreach ($projects as $project): ?>
                    <!-- Featured Image -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="gallery-item">
                            <img src="<?php echo UPLOADS_URL; ?>/projects/<?php echo $project['featured_image']; ?>" 
                                 alt="<?php echo htmlspecialchars($project['title']); ?>" 
                                 class="img-fluid gallery-image"
                                 data-bs-toggle="modal" 
                                 data-bs-target="#imageModal"
                                 data-image="<?php echo UPLOADS_URL; ?>/projects/<?php echo $project['featured_image']; ?>"
                                 data-caption="<?php echo htmlspecialchars($project['title']); ?> - <?php echo htmlspecialchars($project['location']); ?>">
                            <div class="gallery-overlay">
                                <div class="gallery-info">
                                    <h6><?php echo htmlspecialchars($project['title']); ?></h6>
                                    <p><?php echo htmlspecialchars($project['location']); ?></p>
                                </div>
                                <div class="gallery-actions">
                                    <button class="btn btn-light btn-sm" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#imageModal"
                                            data-image="<?php echo UPLOADS_URL; ?>/projects/<?php echo $project['featured_image']; ?>"
                                            data-caption="<?php echo htmlspecialchars($project['title']); ?> - <?php echo htmlspecialchars($project['location']); ?>">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <a href="<?php echo BASE_URL; ?>/project.php?slug=<?php echo $project['slug']; ?>" 
                                       class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <?php 
                    // Get additional images for this project
                    $project_images = get_project_images($project['id']);
                    foreach ($project_images as $image): 
                    ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="gallery-item">
                                <img src="<?php echo UPLOADS_URL; ?>/projects/<?php echo $image['image_path']; ?>" 
                                     alt="<?php echo htmlspecialchars($image['caption'] ?: $project['title']); ?>" 
                                     class="img-fluid gallery-image"
                                     data-bs-toggle="modal" 
                                     data-bs-target="#imageModal"
                                     data-image="<?php echo UPLOADS_URL; ?>/projects/<?php echo $image['image_path']; ?>"
                                     data-caption="<?php echo htmlspecialchars($image['caption'] ?: $project['title']); ?>">
                                <div class="gallery-overlay">
                                    <div class="gallery-info">
                                        <h6><?php echo htmlspecialchars($project['title']); ?></h6>
                                        <p><?php echo htmlspecialchars($image['caption'] ?: $project['location']); ?></p>
                                    </div>
                                    <div class="gallery-actions">
                                        <button class="btn btn-light btn-sm" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#imageModal"
                                                data-image="<?php echo UPLOADS_URL; ?>/projects/<?php echo $image['image_path']; ?>"
                                                data-caption="<?php echo htmlspecialchars($image['caption'] ?: $project['title']); ?>">
                                            <i class="fas fa-search-plus"></i>
                                        </button>
                                        <a href="<?php echo BASE_URL; ?>/project.php?slug=<?php echo $project['slug']; ?>" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Services CTA -->
<section class="services-cta py-5 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h3>Impressed by Our Work?</h3>
                <p class="mb-0">Let us bring your construction vision to life with the same quality and attention to detail.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="<?php echo BASE_URL; ?>/services.php" class="btn btn-primary me-2">Our Services</a>
                <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-outline-primary">Get Quote</a>
            </div>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Project Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-0">
                <img src="" alt="" class="img-fluid" id="modalImage" style="max-height: 80vh; width: auto;">
            </div>
            <div class="modal-footer">
                <p class="mb-0 text-muted" id="modalCaption"></p>
            </div>
        </div>
    </div>
</div>

<style>
/* Gallery Styles */
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    cursor: pointer;
    height: 300px;
}

.gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.8), rgba(26, 37, 47, 0.6));
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-item:hover .gallery-image {
    transform: scale(1.1);
}

.gallery-info {
    color: white;
}

.gallery-info h6 {
    margin-bottom: 5px;
    font-weight: 600;
}

.gallery-info p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
}

.gallery-actions {
    text-align: center;
}

.gallery-actions .btn {
    margin: 0 5px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.services-cta {
    border-top: 1px solid #dee2e6;
}

.services-cta h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.services-cta p {
    color: var(--gray-color);
}

@media (max-width: 768px) {
    .gallery-item {
        height: 250px;
    }
    
    .services-cta .col-lg-4 {
        text-align: left !important;
        margin-top: 20px;
    }
    
    .services-cta .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }
}
</style>

<script>
// Image modal functionality
document.addEventListener('DOMContentLoaded', function() {
    const imageModal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const modalCaption = document.getElementById('modalCaption');
    
    // Handle modal show event
    imageModal.addEventListener('show.bs.modal', function(event) {
        const trigger = event.relatedTarget;
        const imageSrc = trigger.getAttribute('data-image');
        const imageCaption = trigger.getAttribute('data-caption');
        
        modalImage.src = imageSrc;
        modalImage.alt = imageCaption;
        modalCaption.textContent = imageCaption;
    });
    
    // Handle keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (imageModal.classList.contains('show')) {
            if (e.key === 'Escape') {
                bootstrap.Modal.getInstance(imageModal).hide();
            }
        }
    });
});
</script>

<?php include_footer(); ?>
