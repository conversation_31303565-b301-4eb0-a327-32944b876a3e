    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <!-- Company Info -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="footer-widget">
                        <img src="<?php echo ASSETS_URL; ?>/images/logo-white.png" alt="<?php echo SITE_NAME; ?>" class="footer-logo mb-3">
                        <p class="footer-description">
                            Our team brings together many years of collective experience in the construction industry embodying extensive knowledge and refined processes.
                        </p>
                        <div class="footer-contact">
                            <p><i class="fas fa-map-marker-alt"></i> <?php echo get_setting('company_address'); ?></p>
                            <p><i class="fas fa-phone"></i> <a href="tel:<?php echo get_setting('company_phone'); ?>"><?php echo get_setting('company_phone'); ?></a></p>
                            <p><i class="fas fa-mobile-alt"></i> <a href="tel:<?php echo get_setting('company_mobile'); ?>"><?php echo get_setting('company_mobile'); ?></a></p>
                            <p><i class="fas fa-envelope"></i> <a href="mailto:<?php echo get_setting('company_email'); ?>"><?php echo get_setting('company_email'); ?></a></p>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="footer-widget">
                        <h5 class="footer-title">Company</h5>
                        <ul class="footer-links">
                            <li><a href="<?php echo BASE_URL; ?>">Home</a></li>
                            <li><a href="<?php echo BASE_URL; ?>/about.php">About Us</a></li>
                            <li><a href="<?php echo BASE_URL; ?>/services.php">Our Services</a></li>
                            <li><a href="<?php echo BASE_URL; ?>/projects.php">Our Projects</a></li>
                            <li><a href="<?php echo BASE_URL; ?>/gallery.php">Gallery</a></li>
                            <li><a href="<?php echo BASE_URL; ?>/contact.php">Contact Us</a></li>
                        </ul>
                    </div>
                </div>
                
                <!-- Services -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="footer-widget">
                        <h5 class="footer-title">Services</h5>
                        <ul class="footer-links">
                            <?php 
                            $footer_services = get_services('active');
                            foreach ($footer_services as $service): 
                            ?>
                                <li><a href="<?php echo BASE_URL; ?>/service.php?slug=<?php echo $service['slug']; ?>"><?php echo htmlspecialchars($service['title']); ?></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
                
                <!-- Social Media & Newsletter -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="footer-widget">
                        <h5 class="footer-title">Follow Us</h5>
                        <div class="footer-social">
                            <?php if (get_setting('facebook_url')): ?>
                                <a href="<?php echo get_setting('facebook_url'); ?>" target="_blank" class="social-link">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                            <?php endif; ?>
                            <?php if (get_setting('instagram_url')): ?>
                                <a href="<?php echo get_setting('instagram_url'); ?>" target="_blank" class="social-link">
                                    <i class="fab fa-instagram"></i>
                                </a>
                            <?php endif; ?>
                            <?php if (get_setting('linkedin_url')): ?>
                                <a href="<?php echo get_setting('linkedin_url'); ?>" target="_blank" class="social-link">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                            <?php endif; ?>
                            <?php if (get_setting('youtube_url')): ?>
                                <a href="<?php echo get_setting('youtube_url'); ?>" target="_blank" class="social-link">
                                    <i class="fab fa-youtube"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                        
                        <div class="newsletter mt-4">
                            <h6>Get Updates</h6>
                            <form class="newsletter-form" action="<?php echo BASE_URL; ?>/contact.php" method="POST">
                                <div class="input-group">
                                    <input type="email" class="form-control" name="email" placeholder="Your email" required>
                                    <button class="btn btn-primary" type="submit" name="newsletter">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer Bottom -->
            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="copyright">
                            &copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.
                        </p>
                    </div>
                    <div class="col-md-6">
                        <div class="footer-bottom-links">
                            <a href="<?php echo BASE_URL; ?>/privacy.php">Privacy Policy</a>
                            <a href="<?php echo BASE_URL; ?>/terms.php">Terms of Service</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop">
        <i class="fas fa-chevron-up"></i>
    </button>
    
    <!-- JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
</body>
</html>
