<?php
/**
 * Common Functions
 * Flori Construction Ltd Website
 */

require_once dirname(__DIR__) . '/config/config.php';

// Content management functions
function get_content($page, $section, $key, $default = '') {
    $db = getDB();
    $stmt = $db->prepare("SELECT content_value FROM content WHERE page = ? AND section = ? AND content_key = ?");
    $stmt->execute([$page, $section, $key]);
    $result = $stmt->fetch();
    return $result ? $result['content_value'] : $default;
}

function update_content($page, $section, $key, $value, $type = 'text') {
    $db = getDB();
    $stmt = $db->prepare("INSERT INTO content (page, section, content_key, content_value, content_type) 
                         VALUES (?, ?, ?, ?, ?) 
                         ON DUPLICATE KEY UPDATE content_value = ?, content_type = ?, updated_at = CURRENT_TIMESTAMP");
    return $stmt->execute([$page, $section, $key, $value, $type, $value, $type]);
}

// Settings functions
function get_setting($key, $default = '') {
    $db = getDB();
    $stmt = $db->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch();
    return $result ? $result['setting_value'] : $default;
}

function update_setting($key, $value, $type = 'text', $description = '') {
    $db = getDB();
    $stmt = $db->prepare("INSERT INTO settings (setting_key, setting_value, setting_type, description) 
                         VALUES (?, ?, ?, ?) 
                         ON DUPLICATE KEY UPDATE setting_value = ?, setting_type = ?, updated_at = CURRENT_TIMESTAMP");
    return $stmt->execute([$key, $value, $type, $description, $value, $type]);
}

// Services functions
function get_services($status = 'active') {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM services WHERE status = ? ORDER BY sort_order ASC, title ASC");
    $stmt->execute([$status]);
    return $stmt->fetchAll();
}

function get_service_by_slug($slug) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM services WHERE slug = ? AND status = 'active'");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

function get_service_by_id($id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM services WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

// Projects functions
function get_projects($category = null, $status = 'active', $limit = null) {
    $db = getDB();
    $sql = "SELECT * FROM projects WHERE status = ?";
    $params = [$status];
    
    if ($category) {
        $sql .= " AND category = ?";
        $params[] = $category;
    }
    
    $sql .= " ORDER BY sort_order ASC, completion_date DESC";
    
    if ($limit) {
        $sql .= " LIMIT ?";
        $params[] = $limit;
    }
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

function get_project_by_slug($slug) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM projects WHERE slug = ? AND status = 'active'");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

function get_project_by_id($id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM projects WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

function get_project_images($project_id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM project_images WHERE project_id = ? ORDER BY sort_order ASC, id ASC");
    $stmt->execute([$project_id]);
    return $stmt->fetchAll();
}

// Contact functions
function save_contact_submission($name, $email, $phone, $subject, $message) {
    $db = getDB();
    $stmt = $db->prepare("INSERT INTO contact_submissions (name, email, phone, subject, message) VALUES (?, ?, ?, ?, ?)");
    return $stmt->execute([$name, $email, $phone, $subject, $message]);
}

function get_contact_submissions($status = null) {
    $db = getDB();
    $sql = "SELECT * FROM contact_submissions";
    $params = [];
    
    if ($status) {
        $sql .= " WHERE status = ?";
        $params[] = $status;
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

// File upload functions
function upload_image($file, $directory = 'general') {
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['success' => false, 'message' => 'No file uploaded'];
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'File size too large'];
    }
    
    if (!is_valid_image($file['name'])) {
        return ['success' => false, 'message' => 'Invalid file type'];
    }
    
    $upload_dir = UPLOADS_PATH . '/' . $directory;
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $extension = get_file_extension($file['name']);
    $filename = uniqid() . '.' . $extension;
    $filepath = $upload_dir . '/' . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $directory . '/' . $filename];
    } else {
        return ['success' => false, 'message' => 'Failed to upload file'];
    }
}

// Template functions
function include_header($title = '', $description = '') {
    $page_title = $title ? $title . ' - ' . SITE_NAME : SITE_NAME;
    include INCLUDES_PATH . '/header.php';
}

function include_footer() {
    include INCLUDES_PATH . '/footer.php';
}

function include_admin_header($title = '') {
    $page_title = $title ? $title . ' - Admin - ' . SITE_NAME : 'Admin - ' . SITE_NAME;
    include ADMIN_PATH . '/includes/header.php';
}

function include_admin_footer() {
    include ADMIN_PATH . '/includes/footer.php';
}

// Admin authentication functions
function require_admin_login() {
    if (!is_admin_logged_in()) {
        redirect(ADMIN_URL . '/login.php');
    }
}

function is_admin_logged_in() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}


?>
