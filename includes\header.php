<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) : SITE_NAME; ?></title>
    <meta name="description" content="<?php echo isset($description) ? htmlspecialchars($description) : 'Professional construction services in London. Specializing in civil engineering, groundworks, RC frames, basements, and hard landscaping.'; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/fontawesome.min.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/style.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="contact-info">
                            <a href="tel:<?php echo get_setting('company_phone'); ?>" class="contact-item">
                                <i class="fas fa-phone"></i>
                                <?php echo get_setting('company_phone'); ?>
                            </a>
                            <a href="mailto:<?php echo get_setting('company_email'); ?>" class="contact-item">
                                <i class="fas fa-envelope"></i>
                                <?php echo get_setting('company_email'); ?>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="social-links">
                            <?php if (get_setting('facebook_url')): ?>
                                <a href="<?php echo get_setting('facebook_url'); ?>" target="_blank" class="social-link">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                            <?php endif; ?>
                            <?php if (get_setting('instagram_url')): ?>
                                <a href="<?php echo get_setting('instagram_url'); ?>" target="_blank" class="social-link">
                                    <i class="fab fa-instagram"></i>
                                </a>
                            <?php endif; ?>
                            <?php if (get_setting('linkedin_url')): ?>
                                <a href="<?php echo get_setting('linkedin_url'); ?>" target="_blank" class="social-link">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                            <?php endif; ?>
                            <?php if (get_setting('youtube_url')): ?>
                                <a href="<?php echo get_setting('youtube_url'); ?>" target="_blank" class="social-link">
                                    <i class="fab fa-youtube"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container">
                <!-- Logo -->
                <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                    <img src="<?php echo ASSETS_URL; ?>/images/logo.png" alt="<?php echo SITE_NAME; ?>" class="logo">
                </a>
                
                <!-- Mobile Menu Toggle -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <!-- Navigation Menu -->
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'about.php' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/about.php">About Us</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'services.php' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/services.php">Our Services</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo in_array(basename($_SERVER['PHP_SELF']), ['projects.php', 'project.php']) ? 'active' : ''; ?>" href="#" id="projectsDropdown" role="button" data-bs-toggle="dropdown">
                                Our Projects
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/projects.php?category=completed">Completed Projects</a></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/projects.php?category=ongoing">Ongoing Projects</a></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/projects.php">All Projects</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'gallery.php' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/gallery.php">Gallery</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'contact.php' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/contact.php">Contact Us</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    
    <!-- Main Content -->
