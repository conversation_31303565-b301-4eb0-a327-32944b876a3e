<?php
/**
 * Installation Script for Flori Construction Ltd Website
 * Run this script once to set up the website
 */

// Check if already installed
if (file_exists('config/installed.lock')) {
    die('Website is already installed. Delete config/installed.lock to reinstall.');
}

$errors = [];
$success = [];

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0') < 0) {
    $errors[] = 'PHP 7.4 or higher is required. Current version: ' . PHP_VERSION;
}

// Check required extensions
$required_extensions = ['pdo', 'pdo_mysql', 'gd', 'mbstring'];
foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        $errors[] = "Required PHP extension missing: $ext";
    }
}

// Check directory permissions
$directories = [
    'uploads',
    'uploads/services',
    'uploads/projects',
    'uploads/gallery',
    'config'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (!mkdir($dir, 0755, true)) {
            $errors[] = "Cannot create directory: $dir";
        } else {
            $success[] = "Created directory: $dir";
        }
    }
    
    if (!is_writable($dir)) {
        $errors[] = "Directory not writable: $dir";
    }
}

// Handle form submission
if ($_POST) {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_name = $_POST['db_name'] ?? 'flori_construction';
    $db_user = $_POST['db_user'] ?? '';
    $db_pass = $_POST['db_pass'] ?? '';
    $site_url = $_POST['site_url'] ?? '';
    $admin_email = $_POST['admin_email'] ?? '';
    $admin_password = $_POST['admin_password'] ?? '';
    
    if (empty($db_user) || empty($site_url) || empty($admin_email) || empty($admin_password)) {
        $errors[] = 'Please fill in all required fields.';
    } else {
        try {
            // Test database connection
            $dsn = "mysql:host=$db_host;dbname=$db_name;charset=utf8mb4";
            $pdo = new PDO($dsn, $db_user, $db_pass, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            // Import database schema
            $sql = file_get_contents('database.sql');
            $pdo->exec($sql);
            $success[] = 'Database schema imported successfully.';
            
            // Update admin user
            $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET email = ?, password = ? WHERE username = 'admin'");
            $stmt->execute([$admin_email, $hashed_password]);
            $success[] = 'Admin user updated successfully.';
            
            // Update configuration files
            $config_content = file_get_contents('config/config.php');
            $config_content = str_replace("'http://localhost/flori-con'", "'$site_url'", $config_content);
            file_put_contents('config/config.php', $config_content);
            
            $db_config_content = file_get_contents('config/database.php');
            $db_config_content = str_replace("define('DB_HOST', 'localhost');", "define('DB_HOST', '$db_host');", $db_config_content);
            $db_config_content = str_replace("define('DB_NAME', 'flori_construction');", "define('DB_NAME', '$db_name');", $db_config_content);
            $db_config_content = str_replace("define('DB_USER', 'root');", "define('DB_USER', '$db_user');", $db_config_content);
            $db_config_content = str_replace("define('DB_PASS', '');", "define('DB_PASS', '$db_pass');", $db_config_content);
            file_put_contents('config/database.php', $db_config_content);
            
            $success[] = 'Configuration files updated successfully.';
            
            // Create placeholder images if GD is available
            if (extension_loaded('gd')) {
                include 'create_placeholders.php';
                $success[] = 'Placeholder images created successfully.';
            }
            
            // Create installation lock file
            file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
            $success[] = 'Installation completed successfully!';
            
            // Redirect to admin login
            echo '<script>setTimeout(function(){ window.location.href = "admin/login.php"; }, 3000);</script>';
            
        } catch (Exception $e) {
            $errors[] = 'Database error: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install - Flori Construction Ltd</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .install-container { max-width: 600px; margin: 50px auto; }
        .install-card { background: white; border-radius: 10px; padding: 40px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .logo { text-align: center; margin-bottom: 30px; }
        .logo h1 { color: #2c3e50; font-weight: 700; }
        .status-item { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .status-success { background-color: #d4edda; color: #155724; }
        .status-error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-card">
            <div class="logo">
                <h1>Flori Construction Ltd</h1>
                <p>Website Installation</p>
            </div>
            
            <?php if (!empty($errors)): ?>
                <div class="mb-4">
                    <h5>Errors:</h5>
                    <?php foreach ($errors as $error): ?>
                        <div class="status-item status-error"><?php echo htmlspecialchars($error); ?></div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="mb-4">
                    <h5>Success:</h5>
                    <?php foreach ($success as $msg): ?>
                        <div class="status-item status-success"><?php echo htmlspecialchars($msg); ?></div>
                    <?php endforeach; ?>
                </div>
                
                <?php if (in_array('Installation completed successfully!', $success)): ?>
                    <div class="alert alert-success">
                        <h5>Installation Complete!</h5>
                        <p>You will be redirected to the admin login page in 3 seconds...</p>
                        <a href="admin/login.php" class="btn btn-primary">Go to Admin Login</a>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
            
            <?php if (empty($success) || !in_array('Installation completed successfully!', $success)): ?>
                <form method="POST">
                    <h5>Database Configuration</h5>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Database Host</label>
                            <input type="text" class="form-control" name="db_host" value="localhost" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Database Name</label>
                            <input type="text" class="form-control" name="db_name" value="flori_construction" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Database Username</label>
                            <input type="text" class="form-control" name="db_user" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Database Password</label>
                            <input type="password" class="form-control" name="db_pass">
                        </div>
                    </div>
                    
                    <h5 class="mt-4">Website Configuration</h5>
                    <div class="mb-3">
                        <label class="form-label">Site URL</label>
                        <input type="url" class="form-control" name="site_url" value="<?php echo 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']); ?>" required>
                        <small class="form-text text-muted">Full URL to your website (without trailing slash)</small>
                    </div>
                    
                    <h5 class="mt-4">Admin Account</h5>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Admin Email</label>
                            <input type="email" class="form-control" name="admin_email" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Admin Password</label>
                            <input type="password" class="form-control" name="admin_password" required>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-lg w-100" <?php echo !empty($errors) ? 'disabled' : ''; ?>>
                        Install Website
                    </button>
                </form>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
