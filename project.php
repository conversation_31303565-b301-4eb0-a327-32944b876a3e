<?php
require_once 'includes/functions.php';

// Get project slug from URL
$slug = isset($_GET['slug']) ? sanitize_input($_GET['slug']) : '';

if (empty($slug)) {
    redirect(BASE_URL . '/projects.php');
}

// Get project details
$project = get_project_by_slug($slug);

if (!$project) {
    redirect(BASE_URL . '/projects.php');
}

// Get project images
$project_images = get_project_images($project['id']);

$page_title = htmlspecialchars($project['title']);
$description = htmlspecialchars($project['short_description']);

include_header($page_title, $description);
?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="page-header-content">
                    <h1 class="page-title"><?php echo htmlspecialchars($project['title']); ?></h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>">Home</a></li>
                            <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>/projects.php">Projects</a></li>
                            <li class="breadcrumb-item active" aria-current="page"><?php echo htmlspecialchars($project['title']); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Project Details -->
<section class="project-details py-5">
    <div class="container">
        <div class="row">
            <!-- Project Info -->
            <div class="col-lg-8 mb-5">
                <!-- Featured Image -->
                <div class="project-featured-image mb-4">
                    <img src="<?php echo UPLOADS_URL; ?>/projects/<?php echo $project['featured_image']; ?>" 
                         alt="<?php echo htmlspecialchars($project['title']); ?>" 
                         class="img-fluid rounded">
                </div>
                
                <!-- Project Description -->
                <div class="project-description">
                    <h3>Project Overview</h3>
                    <p class="lead"><?php echo htmlspecialchars($project['short_description']); ?></p>
                    
                    <?php if ($project['description']): ?>
                        <div class="project-full-description">
                            <?php echo nl2br(htmlspecialchars($project['description'])); ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Project Gallery -->
                <?php if (!empty($project_images)): ?>
                    <div class="project-gallery mt-5">
                        <h3>Project Gallery</h3>
                        <div class="row">
                            <?php foreach ($project_images as $image): ?>
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <div class="gallery-item">
                                        <img src="<?php echo UPLOADS_URL; ?>/projects/<?php echo $image['image_path']; ?>" 
                                             alt="<?php echo htmlspecialchars($image['caption'] ?: $project['title']); ?>" 
                                             class="img-fluid rounded gallery-image"
                                             data-bs-toggle="modal" 
                                             data-bs-target="#imageModal"
                                             data-image="<?php echo UPLOADS_URL; ?>/projects/<?php echo $image['image_path']; ?>"
                                             data-caption="<?php echo htmlspecialchars($image['caption'] ?: $project['title']); ?>">
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Project Sidebar -->
            <div class="col-lg-4">
                <div class="project-sidebar">
                    <!-- Project Info Card -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title">Project Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="project-info-item">
                                <strong>Client:</strong>
                                <span><?php echo htmlspecialchars($project['client']); ?></span>
                            </div>
                            
                            <div class="project-info-item">
                                <strong>Location:</strong>
                                <span><?php echo htmlspecialchars($project['location']); ?></span>
                            </div>
                            
                            <div class="project-info-item">
                                <strong>Category:</strong>
                                <span class="badge bg-<?php echo $project['category'] === 'completed' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst($project['category']); ?>
                                </span>
                            </div>
                            
                            <?php if ($project['completion_date']): ?>
                                <div class="project-info-item">
                                    <strong>Completion Date:</strong>
                                    <span><?php echo format_date($project['completion_date']); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($project['project_value']): ?>
                                <div class="project-info-item">
                                    <strong>Project Value:</strong>
                                    <span>£<?php echo number_format($project['project_value']); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Contact Card -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title">Interested in Similar Work?</h5>
                        </div>
                        <div class="card-body">
                            <p>Contact us to discuss your construction project requirements.</p>
                            <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-primary w-100">Get Quote</a>
                        </div>
                    </div>
                    
                    <!-- Services Card -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Our Services</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <?php 
                                $services = get_services('active');
                                foreach ($services as $service): 
                                ?>
                                    <li class="mb-2">
                                        <a href="<?php echo BASE_URL; ?>/service.php?slug=<?php echo $service['slug']; ?>" 
                                           class="text-decoration-none">
                                            <i class="fas fa-arrow-right text-primary me-2"></i>
                                            <?php echo htmlspecialchars($service['title']); ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Projects -->
<section class="related-projects py-5 bg-light">
    <div class="container">
        <div class="section-header text-center mb-5">
            <span class="section-subtitle">Related Work</span>
            <h2 class="section-title">Other Projects</h2>
        </div>
        
        <div class="row">
            <?php 
            // Get related projects (same category, excluding current project)
            $related_projects = get_projects($project['category'], 'active', 3);
            $related_projects = array_filter($related_projects, function($p) use ($project) {
                return $p['id'] !== $project['id'];
            });
            $related_projects = array_slice($related_projects, 0, 3);
            
            foreach ($related_projects as $related): 
            ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="project-card">
                        <div class="project-image">
                            <img src="<?php echo UPLOADS_URL; ?>/projects/<?php echo $related['featured_image']; ?>" 
                                 alt="<?php echo htmlspecialchars($related['title']); ?>" 
                                 class="img-fluid">
                            <div class="project-overlay">
                                <div class="project-info">
                                    <span class="project-category"><?php echo ucfirst($related['category']); ?> Projects</span>
                                    <h5 class="project-title">
                                        <a href="<?php echo BASE_URL; ?>/project.php?slug=<?php echo $related['slug']; ?>">
                                            <?php echo htmlspecialchars($related['title']); ?>
                                        </a>
                                    </h5>
                                </div>
                                <a href="<?php echo BASE_URL; ?>/project.php?slug=<?php echo $related['slug']; ?>" 
                                   class="project-link">
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?php echo BASE_URL; ?>/projects.php" class="btn btn-primary">View All Projects</a>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Project Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img src="" alt="" class="img-fluid" id="modalImage">
                <p class="mt-3 mb-0" id="modalCaption"></p>
            </div>
        </div>
    </div>
</div>

<script>
// Image modal functionality
document.addEventListener('DOMContentLoaded', function() {
    const imageModal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const modalCaption = document.getElementById('modalCaption');
    
    imageModal.addEventListener('show.bs.modal', function(event) {
        const trigger = event.relatedTarget;
        const imageSrc = trigger.getAttribute('data-image');
        const imageCaption = trigger.getAttribute('data-caption');
        
        modalImage.src = imageSrc;
        modalImage.alt = imageCaption;
        modalCaption.textContent = imageCaption;
    });
});
</script>

<?php include_footer(); ?>
