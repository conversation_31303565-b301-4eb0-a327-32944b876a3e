<?php
require_once 'includes/functions.php';

$page_title = 'Our Projects';
$description = 'View our portfolio of completed and ongoing construction projects. Flori Construction Ltd delivers quality construction services across London.';

// Get category from URL parameter
$category = isset($_GET['category']) ? sanitize_input($_GET['category']) : null;
$valid_categories = ['completed', 'ongoing'];

if ($category && !in_array($category, $valid_categories)) {
    $category = null;
}

// Get projects based on category
$projects = get_projects($category, 'active');

// Set page title based on category
if ($category) {
    $page_title = ucfirst($category) . ' Projects';
    $description = "View our " . $category . " construction projects. Quality construction services by Flori Construction Ltd.";
}

include_header($page_title, $description);
?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="page-header-content">
                    <h1 class="page-title">
                        <?php echo $category ? ucfirst($category) . ' Projects' : 'Our Projects'; ?>
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">
                                <?php echo $category ? ucfirst($category) . ' Projects' : 'Our Projects'; ?>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Projects Filter -->
<section class="projects-filter py-4 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="filter-buttons text-center">
                    <a href="<?php echo BASE_URL; ?>/projects.php" 
                       class="btn <?php echo !$category ? 'btn-primary' : 'btn-outline-primary'; ?> me-2 mb-2">
                        All Projects
                    </a>
                    <a href="<?php echo BASE_URL; ?>/projects.php?category=completed" 
                       class="btn <?php echo $category === 'completed' ? 'btn-primary' : 'btn-outline-primary'; ?> me-2 mb-2">
                        Completed Projects
                    </a>
                    <a href="<?php echo BASE_URL; ?>/projects.php?category=ongoing" 
                       class="btn <?php echo $category === 'ongoing' ? 'btn-primary' : 'btn-outline-primary'; ?> me-2 mb-2">
                        Ongoing Projects
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Projects Grid -->
<section class="projects-grid py-5">
    <div class="container">
        <?php if (empty($projects)): ?>
            <div class="row">
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-building fa-3x text-muted mb-3"></i>
                        <h4>No Projects Found</h4>
                        <p class="text-muted">
                            <?php if ($category): ?>
                                No <?php echo $category; ?> projects are currently available.
                            <?php else: ?>
                                No projects are currently available.
                            <?php endif; ?>
                        </p>
                        <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-primary">Contact Us</a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach ($projects as $project): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="project-card">
                            <div class="project-image">
                                <img src="<?php echo UPLOADS_URL; ?>/projects/<?php echo $project['featured_image']; ?>" 
                                     alt="<?php echo htmlspecialchars($project['title']); ?>" 
                                     class="img-fluid">
                                <div class="project-overlay">
                                    <div class="project-info">
                                        <span class="project-category">
                                            <?php echo ucfirst($project['category']); ?> Projects
                                        </span>
                                        <h5 class="project-title">
                                            <a href="<?php echo BASE_URL; ?>/project.php?slug=<?php echo $project['slug']; ?>">
                                                <?php echo htmlspecialchars($project['title']); ?>
                                            </a>
                                        </h5>
                                        <p class="project-client">
                                            <i class="fas fa-user"></i> <?php echo htmlspecialchars($project['client']); ?>
                                        </p>
                                        <p class="project-location">
                                            <i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($project['location']); ?>
                                        </p>
                                        <?php if ($project['completion_date']): ?>
                                            <p class="project-date">
                                                <i class="fas fa-calendar"></i> <?php echo format_date($project['completion_date']); ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                    <a href="<?php echo BASE_URL; ?>/project.php?slug=<?php echo $project['slug']; ?>" 
                                       class="project-link">
                                        <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Stats Section -->
<section class="stats-section py-5 bg-primary text-white">
    <div class="container">
        <div class="row text-center">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item">
                    <div class="stat-number counter" data-target="<?php echo count(get_projects('completed', 'active')); ?>">0</div>
                    <div class="stat-label">Completed Projects</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item">
                    <div class="stat-number counter" data-target="<?php echo count(get_projects('ongoing', 'active')); ?>">0</div>
                    <div class="stat-label">Ongoing Projects</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item">
                    <div class="stat-number counter" data-target="15">0</div>
                    <div class="stat-label">Years Experience</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item">
                    <div class="stat-number counter" data-target="98">0</div>
                    <div class="stat-label">Client Satisfaction</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h3 class="cta-title">Have a Project in Mind?</h3>
                <p class="cta-description">Let's discuss your construction needs and create something amazing together.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="<?php echo BASE_URL; ?>/contact.php" class="btn btn-primary btn-lg">Start Your Project</a>
            </div>
        </div>
    </div>
</section>

<?php include_footer(); ?>
