<?php
require_once 'includes/functions.php';

// Get service slug from URL
$slug = isset($_GET['slug']) ? sanitize_input($_GET['slug']) : '';

if (empty($slug)) {
    redirect(BASE_URL . '/services.php');
}

// Get service details
$service = get_service_by_slug($slug);

if (!$service) {
    redirect(BASE_URL . '/services.php');
}

$page_title = htmlspecialchars($service['title']);
$description = htmlspecialchars($service['short_description']);

include_header($page_title, $description);
?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="page-header-content">
                    <h1 class="page-title"><?php echo htmlspecialchars($service['title']); ?></h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>">Home</a></li>
                            <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>/services.php">Services</a></li>
                            <li class="breadcrumb-item active" aria-current="page"><?php echo htmlspecialchars($service['title']); ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Service Details -->
<section class="service-details py-5">
    <div class="container">
        <div class="row">
            <!-- Service Content -->
            <div class="col-lg-8 mb-5">
                <!-- Service Image -->
                <div class="service-featured-image mb-4">
                    <img src="<?php echo UPLOADS_URL; ?>/services/<?php echo $service['image']; ?>" 
                         alt="<?php echo htmlspecialchars($service['title']); ?>" 
                         class="img-fluid rounded">
                </div>
                
                <!-- Service Description -->
                <div class="service-description">
                    <h3>Service Overview</h3>
                    <p class="lead"><?php echo htmlspecialchars($service['short_description']); ?></p>
                    
                    <?php if ($service['description']): ?>
                        <div class="service-full-description">
                            <?php echo nl2br(htmlspecialchars($service['description'])); ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Service Features -->
                <div class="service-features mt-5">
                    <h3>What We Offer</h3>
                    <div class="row">
                        <?php
                        // Define service-specific features
                        $features = [];
                        switch($service['slug']) {
                            case 'civil-engineering':
                                $features = [
                                    'Infrastructure Development',
                                    'Road Construction',
                                    'Bridge Construction',
                                    'Public Works Projects',
                                    'Site Planning & Design',
                                    'Project Management'
                                ];
                                break;
                            case 'groundworks':
                                $features = [
                                    'Excavation Services',
                                    'Foundation Construction',
                                    'Drainage Systems',
                                    'Site Preparation',
                                    'Utility Installation',
                                    'Land Clearing'
                                ];
                                break;
                            case 'rc-frames':
                                $features = [
                                    'Reinforced Concrete Structures',
                                    'Commercial Buildings',
                                    'Residential Developments',
                                    'Industrial Facilities',
                                    'Structural Engineering',
                                    'Quality Assurance'
                                ];
                                break;
                            case 'basements':
                                $features = [
                                    'Basement Excavation',
                                    'Waterproofing Solutions',
                                    'Structural Design',
                                    'Underpinning Services',
                                    'Basement Conversions',
                                    'Drainage Systems'
                                ];
                                break;
                            case 'hard-landscaping':
                                $features = [
                                    'Paving & Driveways',
                                    'Retaining Walls',
                                    'Outdoor Construction',
                                    'Garden Features',
                                    'Pathway Construction',
                                    'Decorative Elements'
                                ];
                                break;
                            default:
                                $features = [
                                    'Professional Service',
                                    'Quality Materials',
                                    'Expert Team',
                                    'Timely Delivery',
                                    'Competitive Pricing',
                                    'Customer Support'
                                ];
                        }
                        
                        foreach ($features as $feature):
                        ?>
                            <div class="col-md-6 mb-3">
                                <div class="feature-item">
                                    <i class="fas fa-check-circle text-primary me-2"></i>
                                    <?php echo $feature; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Process -->
                <div class="service-process mt-5">
                    <h3>Our Process</h3>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="process-step text-center">
                                <div class="step-number">1</div>
                                <h6>Consultation</h6>
                                <p>Initial project discussion and requirements analysis</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="process-step text-center">
                                <div class="step-number">2</div>
                                <h6>Planning</h6>
                                <p>Detailed planning and design development</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="process-step text-center">
                                <div class="step-number">3</div>
                                <h6>Execution</h6>
                                <p>Professional implementation with quality control</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="process-step text-center">
                                <div class="step-number">4</div>
                                <h6>Completion</h6>
                                <p>Final inspection and project handover</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Service Sidebar -->
            <div class="col-lg-4">
                <div class="service-sidebar">
                    <!-- Contact Card -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title">Get a Quote</h5>
                        </div>
                        <div class="card-body">
                            <p>Interested in our <?php echo htmlspecialchars($service['title']); ?> services?</p>
                            <a href="<?php echo BASE_URL; ?>/contact.php?service=<?php echo $service['slug']; ?>" 
                               class="btn btn-primary w-100 mb-2">Request Quote</a>
                            <a href="tel:<?php echo get_setting('company_phone'); ?>" 
                               class="btn btn-outline-primary w-100">
                                <i class="fas fa-phone me-2"></i>Call Now
                            </a>
                        </div>
                    </div>
                    
                    <!-- All Services -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title">Our Services</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <?php 
                                $all_services = get_services('active');
                                foreach ($all_services as $s): 
                                    $is_current = $s['id'] === $service['id'];
                                ?>
                                    <li class="mb-2">
                                        <?php if ($is_current): ?>
                                            <span class="text-primary fw-bold">
                                                <i class="fas fa-arrow-right text-primary me-2"></i>
                                                <?php echo htmlspecialchars($s['title']); ?>
                                            </span>
                                        <?php else: ?>
                                            <a href="<?php echo BASE_URL; ?>/service.php?slug=<?php echo $s['slug']; ?>" 
                                               class="text-decoration-none">
                                                <i class="fas fa-arrow-right text-primary me-2"></i>
                                                <?php echo htmlspecialchars($s['title']); ?>
                                            </a>
                                        <?php endif; ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Contact Info -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">Contact Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="contact-item mb-3">
                                <i class="fas fa-phone text-primary me-2"></i>
                                <a href="tel:<?php echo get_setting('company_phone'); ?>">
                                    <?php echo get_setting('company_phone'); ?>
                                </a>
                            </div>
                            <div class="contact-item mb-3">
                                <i class="fas fa-envelope text-primary me-2"></i>
                                <a href="mailto:<?php echo get_setting('company_email'); ?>">
                                    <?php echo get_setting('company_email'); ?>
                                </a>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                <?php echo get_setting('company_address'); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Projects -->
<section class="related-projects py-5 bg-light">
    <div class="container">
        <div class="section-header text-center mb-5">
            <span class="section-subtitle">Our Work</span>
            <h2 class="section-title">Related Projects</h2>
        </div>
        
        <div class="row">
            <?php 
            // Get some projects to display
            $related_projects = get_projects(null, 'active', 3);
            
            foreach ($related_projects as $project): 
            ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="project-card">
                        <div class="project-image">
                            <img src="<?php echo UPLOADS_URL; ?>/projects/<?php echo $project['featured_image']; ?>" 
                                 alt="<?php echo htmlspecialchars($project['title']); ?>" 
                                 class="img-fluid">
                            <div class="project-overlay">
                                <div class="project-info">
                                    <span class="project-category"><?php echo ucfirst($project['category']); ?> Projects</span>
                                    <h5 class="project-title">
                                        <a href="<?php echo BASE_URL; ?>/project.php?slug=<?php echo $project['slug']; ?>">
                                            <?php echo htmlspecialchars($project['title']); ?>
                                        </a>
                                    </h5>
                                </div>
                                <a href="<?php echo BASE_URL; ?>/project.php?slug=<?php echo $project['slug']; ?>" 
                                   class="project-link">
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="<?php echo BASE_URL; ?>/projects.php" class="btn btn-primary">View All Projects</a>
        </div>
    </div>
</section>

<?php include_footer(); ?>
