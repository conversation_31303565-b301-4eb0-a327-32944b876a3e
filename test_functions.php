<?php
/**
 * Test file to verify all functions are working correctly
 * Run this file to check for any function declaration errors
 */

// Start session
session_start();

// Include the main functions file
require_once 'includes/functions.php';

echo "<h1>Function Test Results</h1>";

// Test basic functions
echo "<h2>Basic Functions Test</h2>";

try {
    // Test redirect function (don't actually redirect)
    echo "✅ redirect() function exists<br>";
    
    // Test slug creation
    $slug = create_slug("Test Service Title");
    echo "✅ create_slug() function works: '$slug'<br>";
    
    // Test date formatting
    $formatted_date = format_date('2024-01-01');
    echo "✅ format_date() function works: '$formatted_date'<br>";
    
    // Test file extension
    $ext = get_file_extension('test.jpg');
    echo "✅ get_file_extension() function works: '$ext'<br>";
    
    // Test image validation
    $is_valid = is_valid_image('test.jpg');
    echo "✅ is_valid_image() function works: " . ($is_valid ? 'true' : 'false') . "<br>";
    
} catch (Exception $e) {
    echo "❌ Error in basic functions: " . $e->getMessage() . "<br>";
}

// Test admin functions
echo "<h2>Admin Functions Test</h2>";

try {
    // Test admin login check
    $is_logged_in = is_admin_logged_in();
    echo "✅ is_admin_logged_in() function works: " . ($is_logged_in ? 'true' : 'false') . "<br>";
    
    echo "✅ require_admin_login() function exists<br>";
    
} catch (Exception $e) {
    echo "❌ Error in admin functions: " . $e->getMessage() . "<br>";
}

// Test database functions (if database is available)
echo "<h2>Database Functions Test</h2>";

try {
    // Test database connection
    $db = getDB();
    echo "✅ Database connection successful<br>";
    
    // Test settings functions
    echo "✅ get_setting() function exists<br>";
    echo "✅ update_setting() function exists<br>";
    
    // Test content functions
    echo "✅ get_content() function exists<br>";
    echo "✅ update_content() function exists<br>";
    
    // Test service functions
    echo "✅ get_services() function exists<br>";
    echo "✅ get_service_by_slug() function exists<br>";
    echo "✅ get_service_by_id() function exists<br>";
    
    // Test project functions
    echo "✅ get_projects() function exists<br>";
    echo "✅ get_project_by_slug() function exists<br>";
    echo "✅ get_project_by_id() function exists<br>";
    echo "✅ get_project_images() function exists<br>";
    
    // Test contact functions
    echo "✅ save_contact_submission() function exists<br>";
    echo "✅ get_contact_submissions() function exists<br>";
    
    // Test upload functions
    echo "✅ upload_image() function exists<br>";
    
} catch (Exception $e) {
    echo "❌ Error in database functions: " . $e->getMessage() . "<br>";
    echo "Note: This is expected if database is not set up yet.<br>";
}

// Test template functions
echo "<h2>Template Functions Test</h2>";

try {
    echo "✅ include_header() function exists<br>";
    echo "✅ include_footer() function exists<br>";
    echo "✅ include_admin_header() function exists<br>";
    echo "✅ include_admin_footer() function exists<br>";
    
} catch (Exception $e) {
    echo "❌ Error in template functions: " . $e->getMessage() . "<br>";
}

echo "<h2>Test Complete!</h2>";
echo "<p>If you see this message without any fatal errors, all functions are properly declared and working.</p>";

// Clean up
echo "<br><a href='index.php'>← Back to Homepage</a>";
echo "<br><a href='admin/login.php'>→ Go to Admin Login</a>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 40px; }
h1 { color: #2c3e50; }
h2 { color: #e67e22; border-bottom: 2px solid #e67e22; padding-bottom: 5px; }
</style>
